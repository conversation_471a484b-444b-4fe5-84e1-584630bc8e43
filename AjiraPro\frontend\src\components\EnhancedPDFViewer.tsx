import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import * as pdfjsLib from 'pdfjs-dist';

// Set PDF.js worker source to use approved CDN
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';

interface EnhancedPDFViewerProps {
  filePath: string;
  className?: string;
  fileName?: string;
  onError?: (error: string) => void;
}

const EnhancedPDFViewer: React.FC<EnhancedPDFViewerProps> = ({
  filePath,
  className = '',
  fileName = 'resume.pdf',
  onError
}) => {
  const { isDark } = useTheme();
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.2);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const pdfDocRef = useRef<any>(null);
  const renderTaskRef = useRef<any>(null);

  // Function to get auth token from localStorage
  const getAuthToken = () => {
    try {
      const session = localStorage.getItem('sb-xmmbjopzpyotkyyfqzyq-auth-token');
      if (session) {
        const parsedSession = JSON.parse(session);
        return parsedSession?.access_token;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return null;
  };

  // Function to fetch signed URL from backend
  const fetchSignedUrl = async (): Promise<string | null> => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(
        `${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/resumes/pdf-preview/${encodeURIComponent(filePath)}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Access denied. You can only view your own files.');
        } else if (response.status === 404) {
          throw new Error('File not found.');
        } else if (response.status === 429) {
          throw new Error('Too many requests. Please try again later.');
        }
        throw new Error(`Server error: ${response.status}`);
      }

      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error fetching signed URL:', error);
      throw error;
    }
  };

  // Function to load and render PDF
  const loadPdf = async (url: string) => {
    try {
      setIsLoading(true);
      
      // Cancel any ongoing render task
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }

      // Load PDF document
      const loadingTask = pdfjsLib.getDocument({
        url,
        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
        cMapPacked: true,
      });

      const pdf = await loadingTask.promise;
      pdfDocRef.current = pdf;
      setNumPages(pdf.numPages);
      
      // Render first page
      await renderPage(pdf, 1);
      setError(null);
    } catch (error: any) {
      console.error('Error loading PDF:', error);
      const errorMessage = error.name === 'AbortException' 
        ? 'PDF loading was cancelled' 
        : `Failed to load PDF: ${error.message}`;
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to render a specific page
  const renderPage = async (pdf: any, pageNumber: number) => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const page = await pdf.getPage(pageNumber);
      const viewport = page.getViewport({ scale });
      
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      
      const context = canvas.getContext('2d');
      if (!context) return;

      // Cancel any ongoing render task
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      renderTaskRef.current = page.render(renderContext);
      await renderTaskRef.current.promise;
      renderTaskRef.current = null;
    } catch (error: any) {
      if (error.name !== 'RenderingCancelledException') {
        console.error('Error rendering page:', error);
        setError(`Failed to render page: ${error.message}`);
      }
    }
  };

  // Effect to fetch signed URL and load PDF
  useEffect(() => {
    let isMounted = true;
    let refreshInterval: NodeJS.Timeout;

    const loadPdfWithUrl = async () => {
      try {
        const url = await fetchSignedUrl();
        if (url && isMounted) {
          setPdfUrl(url);
          await loadPdf(url);
        }
      } catch (error: any) {
        if (isMounted) {
          setError(error.message);
          onError?.(error.message);
          setIsLoading(false);
        }
      }
    };

    // Initial load
    loadPdfWithUrl();

    // Refresh URL every 50 seconds (before 60-second expiry)
    refreshInterval = setInterval(() => {
      if (isMounted) {
        loadPdfWithUrl();
      }
    }, 50000);

    return () => {
      isMounted = false;
      clearInterval(refreshInterval);
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }
    };
  }, [filePath]);

  // Effect to re-render when page or scale changes
  useEffect(() => {
    if (pdfDocRef.current && currentPage) {
      renderPage(pdfDocRef.current, currentPage);
    }
  }, [currentPage, scale]);

  // Navigation functions
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const resetZoom = () => {
    setScale(1.2);
  };

  // Download function
  const handleDownload = () => {
    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center h-full p-8 ${className}`}>
        <div className={`text-center max-w-md ${isDark ? 'text-red-400' : 'text-red-600'}`}>
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">PDF Preview Error</h3>
          <p className="text-sm mb-4">{error}</p>
          <button
            onClick={handleDownload}
            disabled={!pdfUrl}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isDark 
                ? 'bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-600' 
                : 'bg-blue-500 hover:bg-blue-600 text-white disabled:bg-gray-400'
            } disabled:cursor-not-allowed`}
          >
            Download PDF Instead
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Controls */}
      <div className={`flex items-center justify-between p-4 border-b ${
        isDark ? 'bg-dark-200 border-dark-100' : 'bg-gray-50 border-gray-200'
      }`}>
        <div className="flex items-center space-x-2">
          <button
            onClick={goToPreviousPage}
            disabled={currentPage <= 1 || isLoading}
            className={`p-2 rounded-lg transition-colors ${
              isDark 
                ? 'bg-dark-300 hover:bg-dark-100 text-white disabled:bg-dark-400' 
                : 'bg-white hover:bg-gray-100 text-gray-700 disabled:bg-gray-200'
            } disabled:cursor-not-allowed`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            {isLoading ? 'Loading...' : `${currentPage} of ${numPages}`}
          </span>
          
          <button
            onClick={goToNextPage}
            disabled={currentPage >= numPages || isLoading}
            className={`p-2 rounded-lg transition-colors ${
              isDark 
                ? 'bg-dark-300 hover:bg-dark-100 text-white disabled:bg-dark-400' 
                : 'bg-white hover:bg-gray-100 text-gray-700 disabled:bg-gray-200'
            } disabled:cursor-not-allowed`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <button onClick={zoomOut} disabled={isLoading} className={`p-2 rounded-lg transition-colors ${
            isDark ? 'bg-dark-300 hover:bg-dark-100 text-white' : 'bg-white hover:bg-gray-100 text-gray-700'
          }`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          
          <button onClick={resetZoom} disabled={isLoading} className={`px-3 py-1 text-sm rounded-lg transition-colors ${
            isDark ? 'bg-dark-300 hover:bg-dark-100 text-white' : 'bg-white hover:bg-gray-100 text-gray-700'
          }`}>
            {Math.round(scale * 100)}%
          </button>
          
          <button onClick={zoomIn} disabled={isLoading} className={`p-2 rounded-lg transition-colors ${
            isDark ? 'bg-dark-300 hover:bg-dark-100 text-white' : 'bg-white hover:bg-gray-100 text-gray-700'
          }`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
          
          <button onClick={handleDownload} disabled={!pdfUrl} className={`p-2 rounded-lg transition-colors ${
            isDark ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* PDF Canvas */}
      <div className={`flex-1 overflow-auto ${isDark ? 'bg-dark-300' : 'bg-gray-100'}`}>
        <div className="flex justify-center p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className={isDark ? 'text-gray-300' : 'text-gray-600'}>Loading PDF...</p>
              </div>
            </div>
          ) : (
            <canvas
              ref={canvasRef}
              className={`shadow-lg ${isDark ? 'bg-white' : 'bg-white'} max-w-full h-auto`}
              style={{ maxHeight: 'calc(100vh - 200px)' }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedPDFViewer;
