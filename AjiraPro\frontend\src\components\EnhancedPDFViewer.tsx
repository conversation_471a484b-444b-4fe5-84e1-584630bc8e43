import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import * as pdfjsLib from 'pdfjs-dist';

// Set PDF.js worker source to use approved CDN
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';

interface EnhancedPDFViewerProps {
  filePath: string;
  className?: string;
  fileName?: string;
  onError?: (error: string) => void;
}

const EnhancedPDFViewer: React.FC<EnhancedPDFViewerProps> = ({
  filePath,
  className = '',
  fileName = 'resume.pdf',
  onError
}) => {
  const { isDark } = useTheme();
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState<string>('Loading PDF...');
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [canvasElement, setCanvasElement] = useState<HTMLCanvasElement | null>(null);
  const pdfDocRef = useRef<any>(null);
  const renderTaskRef = useRef<any>(null);



  // Callback ref for canvas - called when canvas mounts
  const canvasCallbackRef = useCallback((canvas: HTMLCanvasElement | null) => {
    if (canvas) {
      // eslint-disable-next-line no-console
      console.log('Canvas element mounted via callback ref');
      setCanvasElement(canvas);
    }
  }, []);

  // Function to get auth token from localStorage
  const getAuthToken = () => {
    try {
      const session = localStorage.getItem('sb-xmmbjopzpyotkyyfqzyq-auth-token');
      if (session) {
        const parsedSession = JSON.parse(session);
        return parsedSession?.access_token;
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error getting auth token:', error);
    }
    return null;
  };

  // Function to fetch signed URL from backend with retry logic
  const fetchSignedUrl = useCallback(async (retryCount = 0): Promise<string | null> => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(
        `${process.env.REACT_APP_API_URL || 'https://fajirapro-production.up.railway.app'}/api/resumes/pdf-preview/${encodeURIComponent(filePath)}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          mode: 'cors',
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Access denied. You can only view your own files.');
        } else if (response.status === 404) {
          // If file not found and we haven't retried much, wait and retry
          if (retryCount < 3) {
            const waitTime = (retryCount + 1) * 2;
            setLoadingMessage(`File not found, waiting for upload to complete... Retrying in ${waitTime} seconds (${retryCount + 1}/3)`);
            // eslint-disable-next-line no-console
            console.log(`File not found, retrying in ${waitTime} seconds... (attempt ${retryCount + 1}/3)`);
            await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
            setLoadingMessage('Retrying PDF preview...');
            return fetchSignedUrl(retryCount + 1);
          }
          throw new Error('File not found. Please ensure the resume has been uploaded successfully.');
        } else if (response.status === 429) {
          throw new Error('Too many requests. Please try again later.');
        }
        throw new Error(`Server error: ${response.status}`);
      }

      const data = await response.json();
      return data.url;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error fetching signed URL:', error);
      throw error;
    }
  }, [filePath]);



  // Function to render a specific page
  const renderPage = useCallback(async (pdf: any, pageNumber: number) => {
    try {
      if (!canvasElement) {
        // eslint-disable-next-line no-console
        console.error('Canvas element not available for rendering');
        setError('Canvas element not available for rendering');
        return;
      }

      // eslint-disable-next-line no-console
      console.log(`Rendering page ${pageNumber} with scale ${scale}`);

      const page = await pdf.getPage(pageNumber);

      // Calculate optimal scale to fit container width while maintaining aspect ratio
      const baseViewport = page.getViewport({ scale: 1.0 });
      const containerWidth = canvasElement.parentElement?.clientWidth || 800;
      const maxWidth = containerWidth - 32; // Account for padding
      const optimalScale = scale === 1.0 ? Math.min(maxWidth / baseViewport.width, 1.5) : scale;

      const viewport = page.getViewport({ scale: optimalScale });

      // eslint-disable-next-line no-console
      console.log('Viewport dimensions:', {
        width: viewport.width,
        height: viewport.height,
        scale: optimalScale,
        containerWidth: maxWidth
      });

      // Set canvas dimensions with device pixel ratio for crisp rendering
      const devicePixelRatio = window.devicePixelRatio || 1;
      canvasElement.height = viewport.height * devicePixelRatio;
      canvasElement.width = viewport.width * devicePixelRatio;

      // Set canvas style to ensure proper display
      canvasElement.style.width = `${viewport.width}px`;
      canvasElement.style.height = `${viewport.height}px`;

      const context = canvasElement.getContext('2d');
      if (!context) {
        // eslint-disable-next-line no-console
        console.error('Could not get canvas context');
        return;
      }

      // Scale context for device pixel ratio
      context.scale(devicePixelRatio, devicePixelRatio);

      // Clear the canvas
      context.clearRect(0, 0, canvasElement.width, canvasElement.height);

      // Cancel any ongoing render task
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      // eslint-disable-next-line no-console
      console.log('Starting PDF render...');
      renderTaskRef.current = page.render(renderContext);
      await renderTaskRef.current.promise;
      renderTaskRef.current = null;
      // eslint-disable-next-line no-console
      console.log('PDF render completed successfully');
    } catch (error: any) {
      if (error.name !== 'RenderingCancelledException') {
        // eslint-disable-next-line no-console
        console.error('Error rendering page:', error);
        setError(`Failed to render page: ${error.message}`);
      }
    }
  }, [scale, canvasElement]);



  // Effect to fetch signed URL and load PDF
  useEffect(() => {
    let isMounted = true;

    const loadPdfWithUrl = async () => {
      try {
        // eslint-disable-next-line no-console
        console.log('Fetching signed URL for file path:', filePath);

        // Inline fetchSignedUrl to avoid dependency issues
        const token = getAuthToken();
        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await fetch(
          `${process.env.REACT_APP_API_URL || 'https://fajirapro-production.up.railway.app'}/api/resumes/pdf-preview/${encodeURIComponent(filePath)}`,
          {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            mode: 'cors',
          }
        );

        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }

        const data = await response.json();
        const url = data.url;

        // eslint-disable-next-line no-console
        console.log('Received signed URL:', url);

        if (url && isMounted) {
          setPdfUrl(url);

          // Inline loadPdf to avoid dependency issues
          // eslint-disable-next-line no-console
          console.log('Loading PDF from URL:', url);
          setIsLoading(true);

          const loadingTask = pdfjsLib.getDocument({
            url,
            cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
            cMapPacked: true,
          });

          const pdf = await loadingTask.promise;
          pdfDocRef.current = pdf;
          setNumPages(pdf.numPages);

          // Don't render here - let the canvas effect handle it
          setError(null);
          setIsLoading(false);
        } else if (!url) {
          // eslint-disable-next-line no-console
          console.error('No URL received from fetchSignedUrl');
        }
      } catch (error: any) {
        // eslint-disable-next-line no-console
        console.error('Error in loadPdfWithUrl:', error);
        if (isMounted) {
          setError(error.message);
          onError?.(error.message);
          setIsLoading(false);
        }
      }
    };

    // Initial load only
    loadPdfWithUrl();

    return () => {
      isMounted = false;
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }
    };
  }, [filePath, onError]);

  // Effect to re-render when page or scale changes
  useEffect(() => {
    if (pdfDocRef.current && currentPage && canvasElement) {
      renderPage(pdfDocRef.current, currentPage);
    }
  }, [currentPage, scale, renderPage, canvasElement]);

  // Effect to render PDF when canvas becomes available
  useEffect(() => {
    if (pdfDocRef.current && canvasElement && currentPage) {
      // eslint-disable-next-line no-console
      console.log('Canvas is now available, rendering PDF...');
      renderPage(pdfDocRef.current, currentPage);
    }
  }, [canvasElement, renderPage, currentPage]);

  // Navigation functions
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const resetZoom = () => {
    setScale(1.0);
  };

  // Download function
  const handleDownload = () => {
    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center h-full p-8 ${className}`}>
        <div className={`text-center max-w-md ${isDark ? 'text-red-400' : 'text-red-600'}`}>
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">PDF Preview Error</h3>
          <p className="text-sm mb-4">{error}</p>
          <button
            onClick={handleDownload}
            disabled={!pdfUrl}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isDark
                ? 'bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-600'
                : 'bg-blue-500 hover:bg-blue-600 text-white disabled:bg-gray-400'
            } disabled:cursor-not-allowed`}
          >
            Download PDF Instead
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Controls */}
      <div className={`flex items-center justify-between p-4 border-b flex-shrink-0 ${
        isDark ? 'bg-dark-200 border-dark-100' : 'bg-gray-50 border-gray-200'
      }`}>
        <div className="flex items-center space-x-2">
          <button
            onClick={goToPreviousPage}
            disabled={currentPage <= 1 || isLoading}
            className={`p-2 rounded-lg transition-colors ${
              isDark
                ? 'bg-dark-300 hover:bg-dark-100 text-white disabled:bg-dark-400'
                : 'bg-white hover:bg-gray-100 text-gray-700 disabled:bg-gray-200'
            } disabled:cursor-not-allowed`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            {isLoading ? 'Loading...' : `${currentPage} of ${numPages}`}
          </span>

          <button
            onClick={goToNextPage}
            disabled={currentPage >= numPages || isLoading}
            className={`p-2 rounded-lg transition-colors ${
              isDark
                ? 'bg-dark-300 hover:bg-dark-100 text-white disabled:bg-dark-400'
                : 'bg-white hover:bg-gray-100 text-gray-700 disabled:bg-gray-200'
            } disabled:cursor-not-allowed`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <button onClick={zoomOut} disabled={isLoading} className={`p-2 rounded-lg transition-colors ${
            isDark ? 'bg-dark-300 hover:bg-dark-100 text-white' : 'bg-white hover:bg-gray-100 text-gray-700'
          }`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>

          <button onClick={resetZoom} disabled={isLoading} className={`px-3 py-1 text-sm rounded-lg transition-colors ${
            isDark ? 'bg-dark-300 hover:bg-dark-100 text-white' : 'bg-white hover:bg-gray-100 text-gray-700'
          }`}>
            {Math.round(scale * 100)}%
          </button>

          <button onClick={zoomIn} disabled={isLoading} className={`p-2 rounded-lg transition-colors ${
            isDark ? 'bg-dark-300 hover:bg-dark-100 text-white' : 'bg-white hover:bg-gray-100 text-gray-700'
          }`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>

          <button onClick={handleDownload} disabled={!pdfUrl} className={`p-2 rounded-lg transition-colors ${
            isDark ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* PDF Canvas */}
      <div className={`flex-1 min-h-0 overflow-hidden ${isDark ? 'bg-dark-300' : 'bg-gray-100'}`}>
        <div className="h-full overflow-auto">
          <div className="flex justify-center items-start p-4 min-h-full">
            {isLoading ? (
              <div className="flex items-center justify-center w-full h-96">
                <div className="text-center">
                  <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p className={isDark ? 'text-gray-300' : 'text-gray-600'}>{loadingMessage}</p>
                </div>
              </div>
            ) : (
              <canvas
                ref={canvasCallbackRef}
                className={`shadow-lg bg-white border ${isDark ? 'border-dark-100' : 'border-gray-200'}`}
                style={{
                  display: 'block',
                  margin: '0 auto',
                  maxWidth: '100%',
                  height: 'auto'
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedPDFViewer;
