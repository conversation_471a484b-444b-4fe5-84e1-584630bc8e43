// Test script for the PDF Proxy Cloudflare Worker
// Run with: node test-worker.js <resume-id>

const fetch = require('node-fetch');

// Get the resume ID from the command line arguments
const resumeId = process.argv[2];

if (!resumeId) {
  console.error('Please provide a resume ID as a command line argument');
  console.error('Usage: node test-worker.js <resume-id>');
  process.exit(1);
}

// Test the worker
async function testWorker() {
  console.log(`Testing PDF proxy for resume ID: ${resumeId}`);
  
  try {
    // Test the worker with a HEAD request
    console.log('Sending HEAD request to check if the worker is responding...');
    const headResponse = await fetch(`https://ajirapro.com/pdf/${resumeId}`, {
      method: 'HEAD',
    });
    
    console.log(`HEAD response status: ${headResponse.status}`);
    console.log('HEAD response headers:');
    headResponse.headers.forEach((value, name) => {
      console.log(`  ${name}: ${value}`);
    });
    
    if (headResponse.status === 200) {
      console.log('✅ Worker is responding correctly to HEAD requests');
    } else {
      console.log('❌ Worker is not responding correctly to HEAD requests');
    }
    
    // Test the worker with a GET request
    console.log('\nSending GET request to check if the worker is returning PDF content...');
    const getResponse = await fetch(`https://ajirapro.com/pdf/${resumeId}`);
    
    console.log(`GET response status: ${getResponse.status}`);
    console.log('GET response headers:');
    getResponse.headers.forEach((value, name) => {
      console.log(`  ${name}: ${value}`);
    });
    
    if (getResponse.status === 200) {
      const contentType = getResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/pdf')) {
        console.log('✅ Worker is returning PDF content');
        
        // Check the size of the response
        const buffer = await getResponse.buffer();
        console.log(`PDF size: ${buffer.length} bytes`);
        
        if (buffer.length > 0) {
          console.log('✅ PDF content is not empty');
        } else {
          console.log('❌ PDF content is empty');
        }
      } else {
        console.log(`❌ Worker is not returning PDF content (Content-Type: ${contentType})`);
      }
    } else {
      console.log('❌ Worker is not responding correctly to GET requests');
    }
    
    console.log('\nTest complete');
  } catch (error) {
    console.error('Error testing worker:', error);
  }
}

testWorker();
