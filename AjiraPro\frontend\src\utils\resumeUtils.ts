import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

/**
 * Upload a resume file to Supabase storage and create a resume record in the database
 * @param file The resume file to upload
 * @param userId The ID of the user who owns the resume
 * @returns The ID of the created resume
 */
export const uploadResume = async (file: File, userId: string): Promise<string> => {
  try {
    // Generate a unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${userId}/${fileName}`;

    // 1. Upload the file to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('resumes')
      .upload(filePath, file);

    if (uploadError) {
      throw new Error(`Error uploading file: ${uploadError.message}`);
    }

    // 2. Create a record in the resumes table
    const { data: resumeData, error: insertError } = await supabase
      .from('resumes')
      .insert({
        user_id: userId,
        title: file.name, // Use the original filename as the title
        status: 'pending_analysis',
        file_path: filePath
      })
      .select()
      .single();

    if (insertError) {
      throw new Error(`Error creating resume record: ${insertError.message}`);
    }

    return resumeData.id;
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Get a resume by ID
 * @param resumeId The ID of the resume to get
 * @returns The resume data
 */
export const getResume = async (resumeId: string) => {
  try {
    const { data, error } = await supabase
      .from('resumes')
      .select('*')
      .eq('id', resumeId)
      .single();

    if (error) {
      throw new Error(`Error fetching resume: ${error.message}`);
    }

    return data;
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Update a resume's ATS score
 * @param resumeId The ID of the resume to update
 * @param atsScore The ATS score to set
 */
export const updateResumeAtsScore = async (resumeId: string, atsScore: number) => {
  try {
    const { error } = await supabase
      .from('resumes')
      .update({ ats_score: atsScore })
      .eq('id', resumeId);

    if (error) {
      throw new Error(`Error updating resume ATS score: ${error.message}`);
    }
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Get the URL for a resume file
 * @param filePath The path of the file in Supabase storage
 * @returns The URL of the file
 */
export const getResumeFileUrl = async (filePath: string) => {
  try {
    const { data, error } = await supabase.storage
      .from('resumes')
      .createSignedUrl(filePath, 60 * 60); // 1 hour expiry

    if (error) {
      throw new Error(`Error creating signed URL: ${error.message}`);
    }

    return data.signedUrl;
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};
