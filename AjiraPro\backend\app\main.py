from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.core.security import get_current_user
from app.services.supabase import supabase
from app.celery_worker import celery
from app import tasks  # Import tasks to register them
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

# Import routers
from app.routers import auth, resume, payment, template

# Rate limiting setup
limiter = Limiter(key_func=get_remote_address)

# Create FastAPI app
# Updated for Railway deployment testing - AjiraProMax project
# Testing CI/CD pipeline - This comment should trigger the backend workflow
app = FastAPI(
    title=settings.APP_NAME,
    description="API for AjiraPro resume and CV builder",
    version="0.1.0",
    docs_url="/api/docs" if settings.DEBUG else None,
    redoc_url="/api/redoc" if settings.DEBUG else None,
)

# Add rate limiting
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Configure CORS
import logging
logger = logging.getLogger(__name__)
logger.info(f"Configuring CORS with origins: {settings.CORS_ORIGINS}")

# Temporarily use wildcard for debugging CORS issues
# TODO: Revert to specific origins once CORS is working
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Temporarily allow all origins for debugging
    allow_credentials=False,  # Must be False when using wildcard origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to AjiraPro API",
        "version": "0.1.0",
        "docs": "/api/docs"
    }

# Simple health check endpoint
@app.get("/")
async def root():
    import os

    # Check environment variables
    env_vars = {
        "SUPABASE_URL": os.environ.get("SUPABASE_URL", "Not set"),
        "SUPABASE_KEY": "Set" if os.environ.get("SUPABASE_KEY") else "Not set",
        "REDIS_URL": "Set" if os.environ.get("REDIS_URL") else "Not set",
        "ENVIRONMENT": os.environ.get("ENVIRONMENT", "Not set"),
        "DEBUG": os.environ.get("DEBUG", "Not set"),
    }

    return {
        "message": "AjiraPro API is running",
        "environment_variables": env_vars
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    import os
    import logging

    logger = logging.getLogger(__name__)

    # Log environment variables
    logger.info(f"SUPABASE_URL: {'Set' if os.environ.get('SUPABASE_URL') else 'Not set'}")
    logger.info(f"SUPABASE_KEY: {'Set' if os.environ.get('SUPABASE_KEY') else 'Not set'}")
    logger.info(f"REDIS_URL: {'Set' if os.environ.get('REDIS_URL') else 'Not set'}")

    # Check Supabase connection
    try:
        if not settings.SUPABASE_URL or not settings.SUPABASE_KEY:
            raise ValueError("Supabase credentials not set")

        # Check if supabase client exists
        if supabase is None:
            raise ValueError("Supabase client is None")

        # Simple query to check if Supabase is accessible
        supabase.table("profiles").select("count", count="exact").limit(1).execute()
        supabase_status = "healthy"
    except Exception as e:
        logger.error(f"Supabase health check failed: {str(e)}")
        supabase_status = f"unhealthy: {str(e)}"

    # Check Redis and Celery connection
    try:
        # Send a simple ping task to Celery
        celery_ping = celery.control.ping(timeout=1.0)
        redis_celery_status = "healthy" if celery_ping else "unhealthy: no workers responded"
    except Exception as e:
        logger.error(f"Redis/Celery health check failed: {str(e)}")
        redis_celery_status = f"unhealthy: {str(e)}"

    return {
        "status": "running",
        "environment": settings.ENVIRONMENT,
        "supabase": supabase_status,
        "redis_celery": redis_celery_status,
        "environment_variables": {
            "SUPABASE_URL": "Set" if settings.SUPABASE_URL else "Not set",
            "SUPABASE_KEY": "Set" if settings.SUPABASE_KEY else "Not set",
            "REDIS_URL": "Set" if settings.REDIS_URL else "Not set",
        }
    }

# Protected endpoint example
@app.get("/api/me")
async def get_me(user: dict = Depends(get_current_user)):
    return {
        "id": user.id,
        "email": user.email,
        "last_sign_in_at": user.last_sign_in_at
    }

# Test Celery task endpoint
@app.post("/api/test-task")
async def test_task():
    # This will run the task asynchronously
    task = tasks.generate_resume_task.delay({"resume_id": "test-resume", "test": True})

    return {
        "message": "Task submitted successfully",
        "task_id": task.id
    }

# Check task status endpoint
@app.get("/api/task-status/{task_id}")
async def task_status(task_id: str):
    task_result = tasks.generate_resume_task.AsyncResult(task_id)

    result = {
        "task_id": task_id,
        "status": task_result.status,
    }

    if task_result.successful():
        result["result"] = task_result.result
    elif task_result.failed():
        result["error"] = str(task_result.result)

    return result

# Include routers with /api prefix
app.include_router(auth.router, prefix="/api")
app.include_router(resume.router, prefix="/api")
app.include_router(payment.router, prefix="/api")
app.include_router(template.router, prefix="/api")
