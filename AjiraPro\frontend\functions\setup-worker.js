// Setup script for the PDF Proxy Cloudflare Worker
// Run with: node setup-worker.js

const { execSync } = require('child_process');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to execute a command and log the output
function runCommand(command) {
  console.log(`\n> ${command}`);
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    return true;
  } catch (error) {
    console.error(`Error executing command: ${error.message}`);
    return false;
  }
}

// Function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Main setup function
async function setupWorker() {
  console.log('=== PDF Proxy Cloudflare Worker Setup ===');
  
  // Check if wrangler is installed
  try {
    execSync('wrangler --version', { encoding: 'utf8' });
    console.log('✅ Wrangler is installed');
  } catch (error) {
    console.log('❌ Wrangler is not installed');
    const installWrangler = await prompt('Would you like to install Wrangler globally? (y/n): ');
    
    if (installWrangler.toLowerCase() === 'y') {
      if (!runCommand('npm install -g wrangler')) {
        console.error('Failed to install Wrangler. Please install it manually and try again.');
        rl.close();
        return;
      }
    } else {
      console.log('Please install Wrangler and try again.');
      rl.close();
      return;
    }
  }
  
  // Login to Cloudflare
  console.log('\n=== Logging in to Cloudflare ===');
  const loginChoice = await prompt('Would you like to login to Cloudflare now? (y/n): ');
  
  if (loginChoice.toLowerCase() === 'y') {
    runCommand('wrangler login');
  }
  
  // Set up Supabase service role key
  console.log('\n=== Setting up Supabase Service Role Key ===');
  console.log('You will need your Supabase service role key for this step.');
  console.log('You can find this in the Supabase dashboard under Project Settings > API > Project API keys > service_role key.');
  
  const setKeyChoice = await prompt('Would you like to set the Supabase service role key now? (y/n): ');
  
  if (setKeyChoice.toLowerCase() === 'y') {
    const key = await prompt('Enter your Supabase service role key: ');
    
    // Update the worker file with the key
    const workerFilePath = path.join(__dirname, 'pdf', '[[resumeId]].js');
    
    if (fs.existsSync(workerFilePath)) {
      let workerCode = fs.readFileSync(workerFilePath, 'utf8');
      workerCode = workerCode.replace("const SUPABASE_SERVICE_ROLE_KEY = '';", `const SUPABASE_SERVICE_ROLE_KEY = '${key}';`);
      fs.writeFileSync(workerFilePath, workerCode);
      console.log('✅ Updated worker file with service role key');
    } else {
      console.error(`❌ Worker file not found at ${workerFilePath}`);
    }
    
    // Set the secret in wrangler
    console.log('\nSetting the secret in Wrangler...');
    runCommand(`cd "${__dirname}" && wrangler secret put SUPABASE_SERVICE_ROLE_KEY --text "${key}"`);
  }
  
  // Deploy the worker
  console.log('\n=== Deploying the Worker ===');
  const deployChoice = await prompt('Would you like to deploy the worker now? (y/n): ');
  
  if (deployChoice.toLowerCase() === 'y') {
    runCommand(`cd "${__dirname}" && wrangler deploy`);
  }
  
  console.log('\n=== Setup Complete ===');
  console.log('You can now use the PDF proxy worker at https://ajirapro.com/pdf/:resumeId');
  console.log('To test the worker, run: npm run test:worker <resume-id>');
  
  rl.close();
}

setupWorker();
