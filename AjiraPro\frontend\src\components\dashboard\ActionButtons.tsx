import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '../ui';
import { CreateIcon, TailorIcon } from '../ui/icons';

interface ActionButtonsProps {
  tab: 'resumes';
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ tab }) => {
  // We don't need isDark here as Button component handles theming internally
  // const { isDark } = useTheme();

  return (
    <div className="flex flex-wrap gap-2 justify-end">
      <Link to="/resume-builder">
        <Button
          variant="primary"
          size="sm"
          leftIcon={<CreateIcon className="w-4 h-4" />}
        >
          Create New Resume
        </Button>
      </Link>
      <Link to="/tailor-resume">
        <Button
          variant="primary"
          size="sm"
          leftIcon={<TailorIcon className="w-4 h-4" />}
        >
          Tailor Existing Resume
        </Button>
      </Link>
    </div>
  );
};

export default ActionButtons;
