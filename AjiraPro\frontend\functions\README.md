# FajiraPro PDF Proxy Cloudflare Worker

This Cloudflare Worker handles PDF proxying for the FajiraPro application, allowing secure viewing of PDF resumes directly in the browser.

## Quick Deployment Guide

### Prerequisites

1. Cloudflare account with Workers enabled
2. Wrangler CLI installed (`npm install -g wrangler`)
3. Supabase service role key

### Deployment Steps

1. **Login to Cloudflare**:
   ```bash
   wrangler login
   ```

2. **Set the Supabase service role key**:
   ```bash
   wrangler secret put SUPABASE_SERVICE_ROLE_KEY
   ```
   When prompted, enter your Supabase service role key (found in Supabase dashboard under Project Settings > API > Project API keys > service_role key).

3. **Deploy the worker**:
   ```bash
   wrangler deploy
   ```

4. **Test the worker**:
   Visit `https://ajirapro.com/pdf/test` to check if the Supabase connection is working.

## Troubleshooting

### Common Issues

1. **404 Error**: Make sure the worker is deployed and the route is configured correctly.
2. **500 Error**: Check the Supabase service role key is set correctly.
3. **PDF Not Loading**: Verify the resume exists in the database and the file exists in storage.

### Testing

You can test the worker by visiting:
- `https://ajirapro.com/pdf/test` - Tests the Supabase connection
- `https://ajirapro.com/pdf/{resumeId}` - Attempts to load a specific resume

## Worker Functionality

The worker:
1. Receives requests to `/pdf/:resumeId`
2. Fetches the resume data from Supabase to get the file path
3. Generates a signed URL for the file
4. Proxies the PDF content to the client with appropriate security headers

## Security Considerations

The worker implements several security measures:
- Content Security Policy (CSP)
- X-Content-Type-Options
- X-Frame-Options
- CORS headers
- Cache control

## Local Development

To test the worker locally:

```bash
wrangler dev
```

This will start a local development server that you can use to test the worker.

## Environment Variables

- `SUPABASE_SERVICE_ROLE_KEY`: The service role key for your Supabase project (set as a secret)
- `ENVIRONMENT`: The current environment (production, staging, development)
