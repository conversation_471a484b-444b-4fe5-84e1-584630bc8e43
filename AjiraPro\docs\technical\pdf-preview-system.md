# Enhanced PDF Preview System

## Overview

The FajiraPro PDF preview system has been restructured to follow security best practices while providing a seamless user experience. The system uses signed URLs from Supabase Storage, native PDF.js for rendering, and comprehensive security measures.

## Architecture

### Backend (FastAPI)
- **Endpoint**: `/resumes/pdf-preview/{file_path:path}`
- **Authentication**: JWT token validation via Supabase Auth
- **Rate Limiting**: 100 requests per minute per IP
- **Security**: Path traversal protection, user ownership validation

### Frontend (React)
- **Component**: `EnhancedPDFViewer`
- **Library**: Native PDF.js (v3.11.174)
- **Features**: Auto-refresh, error handling, zoom controls, download

### Security
- **Signed URLs**: 60-second expiry from Supabase
- **CORS**: Configured for FajiraPro domains
- **CSP**: Updated to allow PDF.js worker from approved CDN
- **Authentication**: Required for all PDF access

## Implementation Details

### Backend Endpoint

```python
@router.get("/pdf-preview/{file_path:path}")
@limiter.limit("100/minute")
async def get_pdf_preview_url(
    request: Request,
    file_path: str,
    current_user: User = Depends(get_current_user),
):
```

**Security Features:**
1. **Path Validation**: Prevents `../` attacks and validates format
2. **User Ownership**: Ensures users can only access their own files
3. **Rate Limiting**: Prevents abuse with 100 requests/minute limit
4. **Short Expiry**: 60-second signed URLs minimize exposure

### Frontend Component

```typescript
<EnhancedPDFViewer
  filePath="user_id/file_uuid.pdf"
  fileName="resume.pdf"
  className="h-full"
  onError={(error) => handleError(error)}
/>
```

**Features:**
1. **Auto-refresh**: Refreshes signed URL every 50 seconds
2. **Error Handling**: Comprehensive error messages with fallback
3. **Controls**: Page navigation, zoom, download
4. **Loading States**: Visual feedback during PDF loading

## Security Configuration

### Content Security Policy (CSP)

Updated `frontend/public/_headers`:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://ajax.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.ajirapro.com https://api.fajirapro.com; worker-src 'self' blob: https://cdn.jsdelivr.net;
```

### CORS Configuration

Updated `backend/app/core/config.py`:
```python
CORS_ORIGINS: list = [
    "http://localhost:3000",
    "http://localhost:5173",
    "https://ajirapro.com",
    "https://www.ajirapro.com",
    "https://fajirapro.com",
    "https://www.fajirapro.com",
]
```

### Supabase Storage

- **Bucket**: `resumes` (private)
- **File Format**: `user_id/file_uuid.pdf`
- **Access**: Via signed URLs only
- **RLS**: Row-Level Security enabled

## Dependencies

### Backend
```
slowapi==0.1.9  # Rate limiting
```

### Frontend
```
pdfjs-dist==3.11.174  # PDF rendering
```

## Usage Examples

### Basic Usage
```typescript
import EnhancedPDFViewer from '../components/EnhancedPDFViewer';

const MyComponent = () => {
  return (
    <EnhancedPDFViewer
      filePath="user123/resume-456.pdf"
      fileName="my-resume.pdf"
      onError={(error) => console.error(error)}
    />
  );
};
```

### With Error Handling
```typescript
const [error, setError] = useState<string | null>(null);

return (
  <div>
    {error && <div className="error">{error}</div>}
    <EnhancedPDFViewer
      filePath={resumeData.filePath}
      fileName={resumeData.fileName}
      onError={setError}
    />
  </div>
);
```

## Testing

### Test Page
A dedicated test page is available at `/pdf-test` (PDFTestPage component) for:
- Testing different file paths
- Verifying authentication
- Checking error handling
- Validating all features

### Manual Testing Steps
1. **Authentication**: Ensure user is logged in
2. **File Path**: Use format `user_id/file_uuid.pdf`
3. **Backend**: Verify FastAPI endpoint is running
4. **Network**: Check browser dev tools for API calls
5. **Errors**: Test invalid paths and expired tokens

## Error Handling

### Common Errors
1. **401 Unauthorized**: Invalid or expired JWT token
2. **403 Forbidden**: User trying to access another user's file
3. **404 Not Found**: File doesn't exist in storage
4. **429 Too Many Requests**: Rate limit exceeded
5. **500 Server Error**: Backend or Supabase issues

### Error Display
- User-friendly messages in the UI
- Technical details in console for debugging
- Fallback download option when preview fails

## Performance Considerations

### Optimization
1. **Caching**: PDF.js worker cached by Cloudflare CDN
2. **Lazy Loading**: Only first page rendered initially
3. **URL Refresh**: Automatic refresh prevents expiry issues
4. **Rate Limiting**: Prevents server overload

### Monitoring
- Backend logs for API calls and errors
- Frontend error tracking for user issues
- Cloudflare analytics for traffic patterns

## Migration from Old System

### Changes Made
1. **Replaced**: `react-pdf` with native `pdfjs-dist`
2. **Added**: Backend PDF preview endpoint
3. **Enhanced**: Security with rate limiting and validation
4. **Updated**: CSP headers for PDF.js worker
5. **Improved**: Error handling and user feedback

### Breaking Changes
- `PDFViewer` component replaced with `EnhancedPDFViewer`
- Props changed from `fileUrl` to `filePath`
- Requires backend endpoint to be running

## Troubleshooting

### Common Issues
1. **PDF not loading**: Check file path format and user authentication
2. **Worker errors**: Verify CSP allows PDF.js worker from CDN
3. **CORS errors**: Ensure backend CORS includes frontend domain
4. **Rate limiting**: Reduce request frequency or increase limits

### Debug Steps
1. Check browser console for errors
2. Verify network requests in dev tools
3. Test backend endpoint directly
4. Validate file exists in Supabase storage
5. Confirm user has access to the file

## Future Enhancements

### Planned Features
1. **Multi-page rendering**: Render all pages in scrollable view
2. **Annotations**: Add PDF annotation support
3. **Thumbnails**: Generate and cache PDF thumbnails
4. **Search**: Text search within PDFs
5. **Printing**: Direct PDF printing functionality

### Performance Improvements
1. **Caching**: Cache rendered pages client-side
2. **Compression**: Optimize PDF file sizes
3. **CDN**: Serve PDFs through CDN for faster delivery
4. **Progressive Loading**: Load pages on-demand
