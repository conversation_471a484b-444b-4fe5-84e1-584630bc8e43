import React, { useState } from 'react';
import { SearchIcon, DownloadIcon, DeleteIcon } from '../ui/icons';
import { useTheme } from '../../contexts/ThemeContext';

interface TableControlsProps {
  onSearch: (query: string) => void;
  onBatchDelete: () => void;
  onBatchDownload: () => void;
  hasSelection: boolean;
}

const TableControls: React.FC<TableControlsProps> = ({ 
  onSearch, 
  onBatchDelete, 
  onBatchDownload,
  hasSelection 
}) => {
  const { isDark } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
      <form onSubmit={handleSearch} className="w-full sm:w-auto">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon className={`h-5 w-5 ${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
          </div>
          <input
            type="text"
            className={`block w-full sm:w-64 pl-10 pr-3 py-2 border rounded-md text-sm ${
              isDark
                ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500 focus:ring-neon-cyan focus:border-neon-cyan'
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400'
            }`}
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </form>

      <div className={`flex space-x-2 ${!hasSelection ? 'opacity-50 pointer-events-none' : ''}`}>
        <button
          onClick={onBatchDownload}
          disabled={!hasSelection}
          className={`inline-flex items-center px-3 py-1.5 border text-sm font-medium rounded-md ${
            isDark
              ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400'
              : 'bg-white text-blue-600 border-blue-600 hover:bg-blue-50'
          }`}
        >
          <DownloadIcon className="w-4 h-4 mr-1.5" />
          Download Selected
        </button>
        
        <button
          onClick={onBatchDelete}
          disabled={!hasSelection}
          className={`inline-flex items-center px-3 py-1.5 border text-sm font-medium rounded-md ${
            isDark
              ? 'bg-dark-300 text-red-400 border-red-500 hover:bg-dark-400'
              : 'bg-white text-red-600 border-red-600 hover:bg-red-50'
          }`}
        >
          <DeleteIcon className="w-4 h-4 mr-1.5" />
          Delete Selected
        </button>
      </div>
    </div>
  );
};

export default TableControls;
