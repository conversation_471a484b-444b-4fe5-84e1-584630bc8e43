import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up the worker source
pdfjs.GlobalWorkerOptions.workerSrc = `//cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

interface PDFViewerProps {
  fileUrl: string;
  className?: string;
  fileName?: string;
  resumeId?: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  fileUrl,
  className = '',
  fileName = 'resume.pdf'
}) => {
  const { isDark } = useTheme();
  const [isDownloading, setIsDownloading] = useState(false);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to handle document load success
  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
    setIsLoading(false);
    setError(null);
  }

  const handleDownload = () => {
    setIsDownloading(true);

    // Create a hidden link and click it
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset the downloading state after a short delay
    setTimeout(() => {
      setIsDownloading(false);
    }, 1000);
  };

  // Function to handle page navigation
  function changePage(offset: number) {
    if (pageNumber && numPages) {
      const newPageNumber = pageNumber + offset;
      if (newPageNumber >= 1 && newPageNumber <= numPages) {
        setPageNumber(newPageNumber);
      }
    }
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p>Loading PDF preview...</p>
        </div>
      </div>
    );
  }

  // Main PDF viewer with react-pdf
  if (!error) {
    return (
      <div className={`flex flex-col ${className}`}>
        {/* PDF Viewer using react-pdf */}
        <div className={`flex-1 overflow-auto ${isDark ? 'bg-dark-300' : 'bg-gray-100'} rounded-t-lg`}>
          <Document
            file={fileUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={(error) => {
              // Handle PDF loading error
              setError('Error loading PDF. Please try downloading instead.');
              setIsLoading(false);
            }}
            loading={
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p>Loading PDF...</p>
                </div>
              </div>
            }
            className="mx-auto"
          >
            <Page
              pageNumber={pageNumber}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              className={`mx-auto shadow-lg ${isDark ? 'bg-dark-200' : 'bg-white'}`}
              scale={1.0}
            />
          </Document>
        </div>

        {/* Controls and navigation */}
        <div className={`p-3 flex justify-between items-center ${isDark ? 'bg-dark-400' : 'bg-gray-200'} rounded-b-lg`}>
          <div className="flex space-x-2">
            <button
              onClick={previousPage}
              disabled={pageNumber <= 1}
              className={`px-3 py-2 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              } ${pageNumber <= 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <div className={`px-3 py-2 ${isDark ? 'text-white' : 'text-gray-800'}`}>
              Page {pageNumber} of {numPages}
            </div>

            <button
              onClick={nextPage}
              disabled={numPages !== null && pageNumber >= numPages}
              className={`px-3 py-2 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              } ${numPages !== null && pageNumber >= numPages ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className={`px-4 py-2 rounded-md font-medium flex items-center justify-center ${
              isDark
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } ${isDownloading ? 'opacity-75 cursor-not-allowed' : ''}`}
          >
            {isDownloading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Downloading...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download Resume
              </>
            )}
          </button>
        </div>
      </div>
    );
  }

  // Fallback UI if PDF.js fails to load the PDF
  return (
    <div className={`flex flex-col ${className}`}>
      {/* PDF Preview Placeholder */}
      <div className={`flex-1 flex items-center justify-center ${isDark ? 'bg-dark-300 text-white' : 'bg-gray-100 text-gray-800'} rounded-t-lg`}>
        <div className="text-center p-8 max-w-md">
          <svg className={`w-20 h-20 mx-auto mb-6 ${isDark ? 'text-gray-500' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>

          <h3 className={`text-xl font-semibold mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
            PDF Preview Unavailable
          </h3>

          <p className="mb-6">
            {error || "We couldn't display the PDF directly in the browser. Please use the download button below to view your resume."}
          </p>

          <div className="flex flex-col space-y-4">
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className={`px-4 py-3 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              } ${isDownloading ? 'opacity-75 cursor-not-allowed' : ''}`}
            >
              {isDownloading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Downloading...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download Resume
                </>
              )}
            </button>

            <a
              href={fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className={`px-4 py-3 rounded-md font-medium text-center ${
                isDark
                  ? 'bg-gray-700 hover:bg-gray-600 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
              }`}
            >
              Open in New Tab
            </a>

            {/* Retry button for PDF preview */}
            <button
              onClick={() => {
                setIsLoading(true);
                setError(null);
                // Force reload by resetting state
                setNumPages(null);
                setPageNumber(1);
              }}
              className={`px-4 py-3 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              }`}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Retry PDF Preview
            </button>
          </div>
        </div>
      </div>

      {/* Info bar at the bottom */}
      <div className={`p-3 text-center text-sm ${isDark ? 'bg-dark-400 text-gray-300' : 'bg-gray-200 text-gray-600'} rounded-b-lg`}>
        <p>Your resume has been successfully uploaded and analyzed.</p>
        <p className="mt-1 text-xs">
          {isDark ? (
            <span className="text-yellow-400">Note: PDF preview may be temporarily unavailable. Please try again later or use the download options.</span>
          ) : (
            <span className="text-yellow-600">Note: PDF preview may be temporarily unavailable. Please try again later or use the download options.</span>
          )}
        </p>
      </div>
    </div>
  );
};

export default PDFViewer;
