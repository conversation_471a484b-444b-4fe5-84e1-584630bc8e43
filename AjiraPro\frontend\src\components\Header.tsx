import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import ThemeToggle from './ThemeToggle';
import AuthModal from './AuthModal';
import { motion } from 'framer-motion';

const Header: React.FC = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'signup'>('login');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { user, profile, isLoading, signOut, isAuthenticated } = useAuth();
  const { isDark } = useTheme();

  const handleSignOut = async () => {
    try {
      setIsLoggingOut(true);

      // First, clear any local storage items that might be persisting the session
      localStorage.removeItem('supabase.auth.token');

      // Then sign out from Supabase
      await signOut();

      // Set a timeout to ensure the UI updates before navigation
      setTimeout(() => {
        // Reset the loading state
        setIsLoggingOut(false);

        // Navigate to home page
        navigate('/');

        // We won't reload the page here as it might interrupt the navigation
        // The AuthContext should handle the session state change automatically
      }, 500);
    } catch (error) {
      // If there's an exception, reset the loading state and navigate away
      setIsLoggingOut(false);
      navigate('/');
    }
  };

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const openAuthModal = (mode: 'login' | 'signup') => {
    setAuthModalMode(mode);
    setAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setAuthModalOpen(false);
  };

  // Handle clicks outside of the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownRef]);

  return (
    <motion.header
      className="py-4 transition-colors duration-300 dark:bg-dark-200 bg-white shadow-md"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: 'spring', stiffness: 120, damping: 20 }}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/" className={`text-2xl font-bold ${isDark ? 'text-neon-cyan animate-glow' : 'text-blue-600'}`}>
              AjiraPro
            </Link>
          </motion.div>

          {/* Mobile menu button - only visible on mobile */}
          <div className="md:hidden flex items-center space-x-2">
            <button
              className="text-gray-600 dark:text-gray-300 focus:outline-none"
              onClick={toggleMenu}
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {menuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>

            {/* Theme Toggle on mobile */}
            <ThemeToggle />
          </div>

          {/* Desktop navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan transition-colors duration-200">Home</Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/resume-optimizer" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan transition-colors duration-200">Resume Optimizer</Link>
            </motion.div>
            {/* CV Builder link removed */}

            {!isLoading && (
              <>
                {isAuthenticated ? (
                  <div className="flex items-center space-x-4">
                    <div className="relative" ref={dropdownRef}>
                      <motion.button
                        onClick={toggleDropdown}
                        className="flex items-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan focus:outline-none transition-colors duration-200"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="mr-1">{profile?.full_name || user?.email}</span>
                        <svg
                          className={`h-4 w-4 transform ${dropdownOpen ? 'rotate-180' : ''} transition-transform duration-200`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </motion.button>
                    {dropdownOpen && (
                      <motion.div
                        className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-300 rounded-md shadow-lg py-1 z-10"
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Link
                          to="/dashboard"
                          className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors duration-150"
                          onClick={() => setDropdownOpen(false)}
                        >
                          Dashboard
                        </Link>
                        <Link
                          to="/profile"
                          className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors duration-150"
                          onClick={() => setDropdownOpen(false)}
                        >
                          Profile
                        </Link>
                        <motion.button
                          onClick={() => {
                            handleSignOut();
                            setDropdownOpen(false);
                          }}
                          disabled={isLoggingOut}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                          whileHover={{ backgroundColor: isDark ? '#404040' : '#f3f4f6' }}
                        >
                          {isLoggingOut ? 'Signing out...' : 'Sign out'}
                        </motion.button>
                      </motion.div>
                    )}
                    </div>

                    {/* Theme Toggle for desktop - positioned at the far right */}
                    <div className="ml-2">
                      <ThemeToggle />
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-4">
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <button
                        onClick={() => openAuthModal('login')}
                        className={`${isDark ? 'bg-dark-400 hover:bg-dark-500 text-neon-cyan border border-neon-cyan' : 'bg-blue-600 hover:bg-blue-700 text-white'} px-4 py-2 rounded-md transition-colors duration-200`}
                      >
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          Account
                        </span>
                      </button>
                    </motion.div>

                    {/* Theme Toggle for desktop - positioned at the far right */}
                    <div className="ml-2">
                      <ThemeToggle />
                    </div>
                  </div>
                )}
              </>
            )}
          </nav>
        </div>

        {/* Mobile navigation */}
        {menuOpen && (
          <motion.nav
            className="mt-4 md:hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex flex-col space-y-4">
              <motion.div
                whileHover={{ x: 5 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <Link
                  to="/"
                  className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan transition-colors duration-200"
                  onClick={() => setMenuOpen(false)}
                >
                  Home
                </Link>
              </motion.div>
              <motion.div
                whileHover={{ x: 5 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <Link
                  to="/resume-optimizer"
                  className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan transition-colors duration-200"
                  onClick={() => setMenuOpen(false)}
                >
                  Resume Optimizer
                </Link>
              </motion.div>
              {/* CV Builder link removed */}

              {!isLoading && (
                <>
                  {isAuthenticated ? (
                    <>
                      <motion.div
                        whileHover={{ x: 5 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <Link
                          to="/dashboard"
                          className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan transition-colors duration-200"
                          onClick={() => setMenuOpen(false)}
                        >
                          Dashboard
                        </Link>
                      </motion.div>
                      <motion.div
                        whileHover={{ x: 5 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <Link
                          to="/profile"
                          className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan transition-colors duration-200"
                          onClick={() => setMenuOpen(false)}
                        >
                          Profile
                        </Link>
                      </motion.div>
                      <motion.div
                        whileHover={{ x: 5 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <motion.button
                          onClick={() => {
                            handleSignOut();
                            setMenuOpen(false);
                          }}
                          disabled={isLoggingOut}
                          className="text-left text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-neon-cyan disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                          whileTap={{ scale: 0.95 }}
                        >
                          {isLoggingOut ? 'Signing out...' : 'Sign out'}
                        </motion.button>
                      </motion.div>
                    </>
                  ) : (
                    <>
                      <motion.div
                        whileHover={{ x: 5 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <button
                          onClick={() => {
                            openAuthModal('login');
                            setMenuOpen(false);
                          }}
                          className={`${isDark ? 'bg-dark-400 hover:bg-dark-500 text-neon-cyan border border-neon-cyan' : 'bg-blue-600 hover:bg-blue-700 text-white'} px-4 py-2 rounded-md inline-block transition-colors duration-200`}
                        >
                          <span className="flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Account
                          </span>
                        </button>
                      </motion.div>
                    </>
                  )}
                </>
              )}
            </div>
          </motion.nav>
        )}
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={closeAuthModal}
        initialMode={authModalMode}
      />
    </motion.header>
  );
};

export default Header;
