# FajiraPro - Professional Resume Solutions

[![Frontend CI/CD](https://github.com/ProDevDenis/fAjiraPro/actions/workflows/frontend-ci-cd.yml/badge.svg)](https://github.com/ProDevDenis/fAjiraPro/actions/workflows/frontend-ci-cd.yml)
[![Backend CI/CD](https://github.com/ProDevDenis/fAjiraPro/actions/workflows/backend-ci-cd.yml/badge.svg)](https://github.com/ProDevDenis/fAjiraPro/actions/workflows/backend-ci-cd.yml)

**Live Demo:** [https://fajirapro.com](https://fajirapro.com)
1. Introduction / Purpose
This web-based platform is designed to empower job seekers of all levels by providing to them an opportunity to create, enhance, and tailor professional resumes and CVs. The platform assists users in:
I.	Building professional, ATS-optimized resumes and CVs from scratch.
II.	Editing existing resumes and CVs to generate enhanced, ATS-compliant versions.
III.	Crafting targeted resumes, cover letters, and CVs specifically tailored to individual job descriptions.
IV.	Revamping outdated CVs into modern, ATS-friendly formats.
The AI-powered engine streamlines the content creation process and ensures documents are highly compatible with Applicant Tracking Systems (ATS), significantly increasing chances of landing interviews and jobs.
2. Goals and Objectives
- Provide resume and CV creation from scratch for users without CVs or resumes or job descriptions.
- Allow users to generate tailored resumes and CVs from job descriptions.
- Offer ATS-optimization services for existing resumes and CVs.
- Ensure high performance, secure data handling, and fast response times.
- Support flexible payment integration
- Enable scalable growth and easy third-party service integration.
3. Target Audience
•	Job seekers, career changers, students in kenya.
•	Professionals updating CVs and resumes for promotions or industry shifts
4. User pain points:
•	- Difficulty tailoring resumes.
•	- Poor interview call-back rates due to non-optimized resumes.
•	- Limited design skills or writing experience.

Core Features
•	AI-powered cv and resume tailoring based on job descriptions
•	ATS compliance checker with improvement suggestions
•	Step-by-step cv and resume builder with AI guidance (using Prompts questionnaire)
•	CV revamp tool for modernizing existing documents
•	Multiple ATS-friendly templates
•	Cover letter generator based on Job description and Tailored resume/cv.
Tech Stack
•	Frontend: React (TypeScript), Tailwind CSS, Framer Motion
•	Backend: FastAPI (Python), Celery, Redis,…
•	Database: Supabase PostgreSQL
•	Auth: Supabase Auth
•	Storage: Supabase Storage
•	AI/ML: OpenAI GPT, spaCy, LangChain
•	Payments: Flutterwave
## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)
- Python 3.10+
- Git

### Project Structure

```
AjiraPro/
├── .github/            # GitHub Actions workflows
├── AjiraPro/           # Main project directory
│   ├── frontend/       # React frontend application
│   ├── backend/        # FastAPI backend application
│   └── docs/           # Project documentation
├── supabase-project/   # Supabase configuration
└── ...
```

### Environment Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/ProDevDenis/fAjiraPro.git
   cd fAjiraPro
   ```

2. **Frontend Setup**:
   ```bash
   cd AjiraPro/frontend
   npm install --legacy-peer-deps
   cp .env.example .env.local  # Copy example env file and update values
   ```

3. **Backend Setup**:
   ```bash
   cd AjiraPro/backend
   npm install  # For Node.js scripts

   # For Python dependencies
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt

   cp .env.example .env  # Copy example env file and update values
   ```

4. **Supabase Setup**:
   - Create a Supabase project at https://supabase.com
   - Copy your Supabase URL and anon key to the respective .env files
   - Run the database setup scripts:
     ```bash
     cd AjiraPro/backend
     npm run create-buckets
     ```

### Running the Application

1. **Start the frontend development server**:
   ```bash
   cd AjiraPro/frontend
   npm start
   ```
   The frontend will be available at http://localhost:3000

2. **Start the backend development server**:
   ```bash
   cd AjiraPro/backend
   # Start the FastAPI server
   uvicorn app.main:app --reload
   ```
   The backend API will be available at http://localhost:8000

### VS Code Extensions

We recommend installing the following VS Code extensions for the best development experience:

- ESLint
- Prettier
- Tailwind CSS IntelliSense
- Python
- Pylance
- FastAPI
- Thunder Client (for API testing)

See the /docs directory for more detailed documentation on requirements, architecture, and development guidelines.

## CI/CD Pipeline

This project uses GitHub Actions for continuous integration and deployment:

### Frontend CI/CD

The frontend CI/CD pipeline automatically:
- Runs linting and tests on every push and pull request
- Builds the React application
- Deploys to Cloudflare Pages when changes are pushed to the main branch

Required secrets for frontend deployment:
- `CLOUDFLARE_API_TOKEN`: API token for Cloudflare
- `CLOUDFLARE_ACCOUNT_ID`: Your Cloudflare account ID

### Backend CI/CD

The backend CI/CD pipeline automatically:
- Runs linting and tests on every push and pull request
- Deploys to Supabase when changes are pushed to the main branch

Required secrets for backend deployment:
- `SUPABASE_ACCESS_TOKEN`: Access token for Supabase
- `SUPABASE_PROJECT_ID`: Your Supabase project ID

### Setting Up GitHub Secrets

1. Go to your GitHub repository
2. Navigate to Settings > Secrets and variables > Actions
3. Add the required secrets mentioned above

### Local Development

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/AjiraPro.git
   cd AjiraPro
   ```

2. Install dependencies:
   ```
   # For frontend
   cd AjiraPro/frontend
   npm install

   # For backend
   cd ../backend
   npm install
   ```

3. Run the development servers:
   ```
   # For frontend
   cd AjiraPro/frontend
   npm start

   # For backend
   cd ../backend
   # Add your backend start command here
   ```
