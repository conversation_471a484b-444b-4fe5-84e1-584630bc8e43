// Cloudflare Worker for PDF proxying
// This worker handles requests to /pdf/:resumeId and proxies PDF content from Supabase

// Supabase client configuration
const SUPABASE_URL = 'https://xmmbjopzpyotkyyfqzyq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = ''; // This will be set via environment variable

// Headers for CORS and security
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
};

// Security headers for PDF content
const SECURITY_HEADERS = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; object-src 'self'; frame-ancestors 'self'",
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'SAMEORIGIN',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()'
};

// Create Supabase client
const createSupabaseClient = (supabaseKey) => {
  return {
    from: (table) => ({
      select: (columns) => ({
        eq: (column, value) => ({
          single: () => fetch(`${SUPABASE_URL}/rest/v1/${table}?select=${columns}&${column}=eq.${value}`, {
            headers: {
              'apikey': supabaseKey,
              'Authorization': `Bearer ${supabaseKey}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.json())
        })
      })
    }),
    storage: {
      from: (bucket) => ({
        createSignedUrl: (path, expiresIn) => fetch(`${SUPABASE_URL}/storage/v1/object/sign/${bucket}/${path}?expiresIn=${expiresIn}`, {
          method: 'POST',
          headers: {
            'apikey': supabaseKey,
            'Authorization': `Bearer ${supabaseKey}`,
            'Content-Type': 'application/json'
          }
        }).then(res => res.json())
      })
    }
  };
};

// Handle OPTIONS requests for CORS
const handleOptions = () => {
  return new Response(null, {
    status: 204,
    headers: CORS_HEADERS
  });
};

// Handle errors
const handleError = (message, status = 400) => {
  return new Response(JSON.stringify({ error: message }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...CORS_HEADERS
    }
  });
};

// Main handler function
export async function onRequest(context) {
  // Get the request and environment variables
  const { request, env } = context;
  
  // Handle CORS preflight requests
  if (request.method === 'OPTIONS') {
    return handleOptions();
  }
  
  // Only allow GET requests
  if (request.method !== 'GET') {
    return handleError('Method not allowed', 405);
  }
  
  try {
    // Get the resume ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const resumeId = pathParts[pathParts.length - 1];
    
    if (!resumeId) {
      return handleError('Resume ID is required', 400);
    }
    
    // Get the Supabase service role key from environment variables
    const supabaseKey = env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseKey) {
      return handleError('Server configuration error', 500);
    }
    
    // Create Supabase client
    const supabase = createSupabaseClient(supabaseKey);
    
    // Get the resume data from Supabase
    const resumeData = await supabase
      .from('resumes')
      .select('*')
      .eq('id', resumeId)
      .single();
    
    if (!resumeData || !resumeData[0] || !resumeData[0].file_path) {
      return handleError('Resume not found', 404);
    }
    
    const filePath = resumeData[0].file_path;
    
    // Generate a signed URL for the file
    const { data: signedUrlData, error: signedUrlError } = await supabase
      .storage
      .from('resumes')
      .createSignedUrl(filePath, 60 * 5); // 5 minutes expiry
    
    if (signedUrlError || !signedUrlData || !signedUrlData.signedURL) {
      return handleError('Error generating file URL', 500);
    }
    
    // Fetch the PDF content from the signed URL
    const fileResponse = await fetch(signedUrlData.signedURL);
    
    if (!fileResponse.ok) {
      return handleError('Error fetching file', 500);
    }
    
    // Get the file content
    const fileContent = await fileResponse.arrayBuffer();
    
    // Return the PDF content with appropriate headers
    return new Response(fileContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'inline',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        ...CORS_HEADERS,
        ...SECURITY_HEADERS
      }
    });
  } catch (error) {
    console.error('Error in PDF proxy:', error);
    return handleError('Internal server error', 500);
  }
}
