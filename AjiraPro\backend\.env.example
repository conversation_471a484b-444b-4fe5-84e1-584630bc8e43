# Supabase Configuration
SUPABASE_URL=
SUPABASE_KEY=

# OpenAI Configuration
OPENAI_API_KEY=

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Flutterwave Configuration
FLUTTERWAVE_PUBLIC_KEY=your_flutterwave_public_key
FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret_key
FLUTTERWAVE_ENCRYPTION_KEY=your_flutterwave_encryption_key

# Application Configuration
DEBUG=True
ENVIRONMENT=development
