# Cloudflare Workers configuration
name = "ajirapro-pdf-proxy"
main = "pdf-proxy.js"
compatibility_date = "2023-10-30"

# Routes
routes = [
  { pattern = "ajirapro.com/pdf/*", zone_name = "ajirapro.com" }
]

# Environment variables
[vars]
# These are placeholder values - the actual values should be set in the Cloudflare dashboard
# or using wrangler secrets
ENVIRONMENT = "production"

# Secrets that should be set using wrangler secret commands:
# - SUPABASE_SERVICE_ROLE_KEY

# Development environment
[env.development]
name = "ajirapro-pdf-proxy-dev"
vars = { ENVIRONMENT = "development" }

# Staging environment
[env.staging]
name = "ajirapro-pdf-proxy-staging"
vars = { ENVIRONMENT = "staging" }
