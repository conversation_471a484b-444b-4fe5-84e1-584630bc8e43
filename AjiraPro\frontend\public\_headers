# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://ajax.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.ajirapro.com https://api.fajirapro.com http://localhost:8000 https://ajirapro-backend.railway.app; worker-src 'self' blob: https://cdn.jsdelivr.net;
  Strict-Transport-Security: max-age=31536000; includeSubDomains

# Cache static assets
/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images
/*.ico
  Cache-Control: public, max-age=86400
/*.png
  Cache-Control: public, max-age=86400
/*.svg
  Cache-Control: public, max-age=86400
/*.jpg
  Cache-Control: public, max-age=86400
/*.jpeg
  Cache-Control: public, max-age=86400
/*.webp
  Cache-Control: public, max-age=86400

# Don't cache HTML
/*.html
  Cache-Control: public, max-age=0, must-revalidate
