// Deploy script for the PDF Proxy Cloudflare Worker
// Run with: node deploy-worker.js

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to execute a command and log the output
function runCommand(command) {
  console.log(`\n> ${command}`);
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    return true;
  } catch (error) {
    console.error(`Error executing command: ${error.message}`);
    return false;
  }
}

// Function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Main deployment function
async function deployWorker() {
  console.log('=== PDF Proxy Cloudflare Worker Deployment ===');
  
  // Check if wrangler is installed
  try {
    execSync('wrangler --version', { encoding: 'utf8' });
    console.log('✅ Wrangler is installed');
  } catch (error) {
    console.log('❌ Wrangler is not installed');
    const installWrangler = await prompt('Would you like to install Wrangler globally? (y/n): ');
    
    if (installWrangler.toLowerCase() === 'y') {
      if (!runCommand('npm install -g wrangler')) {
        console.error('Failed to install Wrangler. Please install it manually and try again.');
        rl.close();
        return;
      }
    } else {
      console.log('Please install Wrangler and try again.');
      rl.close();
      return;
    }
  }
  
  // Login to Cloudflare
  console.log('\n=== Logging in to Cloudflare ===');
  const loginChoice = await prompt('Would you like to login to Cloudflare now? (y/n): ');
  
  if (loginChoice.toLowerCase() === 'y') {
    runCommand('wrangler login');
  }
  
  // Set up Supabase service role key
  console.log('\n=== Setting up Supabase Service Role Key ===');
  console.log('You will need your Supabase service role key for this step.');
  console.log('You can find this in the Supabase dashboard under Project Settings > API > Project API keys > service_role key.');
  
  const setKeyChoice = await prompt('Would you like to set the Supabase service role key now? (y/n): ');
  
  if (setKeyChoice.toLowerCase() === 'y') {
    const key = await prompt('Enter your Supabase service role key: ');
    
    // Set the secret in wrangler
    console.log('\nSetting the secret in Wrangler...');
    runCommand(`wrangler secret put SUPABASE_SERVICE_ROLE_KEY --text "${key}"`);
  }
  
  // Deploy the worker
  console.log('\n=== Deploying the Worker ===');
  const deployChoice = await prompt('Would you like to deploy the worker now? (y/n): ');
  
  if (deployChoice.toLowerCase() === 'y') {
    runCommand('wrangler deploy');
  }
  
  // Test the worker
  console.log('\n=== Testing the Worker ===');
  const testChoice = await prompt('Would you like to test the worker now? (y/n): ');
  
  if (testChoice.toLowerCase() === 'y') {
    console.log('\nTesting the worker...');
    console.log('Visit https://ajirapro.com/pdf/test in your browser to test the Supabase connection.');
    
    const resumeId = await prompt('Enter a resume ID to test (or press Enter to skip): ');
    
    if (resumeId) {
      console.log(`\nTesting PDF proxy for resume ID: ${resumeId}`);
      console.log(`Visit https://ajirapro.com/pdf/${resumeId} in your browser to test the PDF proxy.`);
    }
  }
  
  console.log('\n=== Deployment Complete ===');
  console.log('You can now use the PDF proxy worker at https://ajirapro.com/pdf/:resumeId');
  
  rl.close();
}

deployWorker();
