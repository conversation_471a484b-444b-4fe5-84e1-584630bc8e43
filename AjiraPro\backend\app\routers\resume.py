from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from ..models.resume import ResumeCreate, ResumeUpdate, ResumeResponse
from ..services.resume import ResumeService
from ..core.security import get_current_user
from ..models.user import User

router = APIRouter(
    prefix="/resumes",
    tags=["resumes"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=ResumeResponse, status_code=status.HTTP_201_CREATED)
async def create_resume(
    resume: ResumeCreate,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Create a new resume for the current user.
    """
    return await resume_service.create_resume(resume, current_user.id)


@router.get("/", response_model=List[ResumeResponse])
async def get_resumes(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Get all resumes for the current user.
    """
    return await resume_service.get_resumes(current_user.id, skip, limit)


@router.get("/{resume_id}", response_model=ResumeResponse)
async def get_resume(
    resume_id: str,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Get a specific resume by ID.
    """
    resume = await resume_service.get_resume(resume_id, current_user.id)
    if not resume:
        raise HTTPException(status_code=404, detail="Resume not found")
    return resume


@router.put("/{resume_id}", response_model=ResumeResponse)
async def update_resume(
    resume_id: str,
    resume: ResumeUpdate,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Update a specific resume by ID.
    """
    updated_resume = await resume_service.update_resume(resume_id, resume, current_user.id)
    if not updated_resume:
        raise HTTPException(status_code=404, detail="Resume not found")
    return updated_resume


@router.delete("/{resume_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_resume(
    resume_id: str,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Delete a specific resume by ID.
    """
    success = await resume_service.delete_resume(resume_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="Resume not found")
    return None


@router.post("/{resume_id}/generate", response_model=ResumeResponse)
async def generate_resume_content(
    resume_id: str,
    job_description: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Generate content for a resume using AI.
    """
    resume = await resume_service.generate_resume_content(resume_id, current_user.id, job_description)
    if not resume:
        raise HTTPException(status_code=404, detail="Resume not found")
    return resume


@router.post("/{resume_id}/analyze", response_model=dict)
async def analyze_resume(
    resume_id: str,
    job_description: str,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Analyze a resume against a job description to get ATS score and suggestions.
    """
    analysis = await resume_service.analyze_resume(resume_id, current_user.id, job_description)
    if not analysis:
        raise HTTPException(status_code=404, detail="Resume not found")
    return analysis


@router.post("/{resume_id}/export", response_model=dict)
async def export_resume(
    resume_id: str,
    format: str = "pdf",
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Export a resume to PDF or other formats.
    """
    export_url = await resume_service.export_resume(resume_id, current_user.id, format)
    if not export_url:
        raise HTTPException(status_code=404, detail="Resume not found")
    return {"url": export_url}
