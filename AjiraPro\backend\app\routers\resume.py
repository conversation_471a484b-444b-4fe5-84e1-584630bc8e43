from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Optional
from ..models.resume import ResumeCreate, ResumeUpdate, ResumeResponse
from ..services.resume import ResumeService
from ..core.security import get_current_user
from ..models.user import User
from ..services.supabase import supabase
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import re
import logging

# Rate limiting setup
limiter = Limiter(key_func=get_remote_address)
security = HTTPBearer()

router = APIRouter(
    prefix="/resumes",
    tags=["resumes"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=ResumeResponse, status_code=status.HTTP_201_CREATED)
async def create_resume(
    resume: ResumeCreate,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Create a new resume for the current user.
    """
    return await resume_service.create_resume(resume, current_user.id)


@router.get("/", response_model=List[ResumeResponse])
async def get_resumes(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Get all resumes for the current user.
    """
    return await resume_service.get_resumes(current_user.id, skip, limit)


@router.get("/{resume_id}", response_model=ResumeResponse)
async def get_resume(
    resume_id: str,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Get a specific resume by ID.
    """
    resume = await resume_service.get_resume(resume_id, current_user.id)
    if not resume:
        raise HTTPException(status_code=404, detail="Resume not found")
    return resume


@router.put("/{resume_id}", response_model=ResumeResponse)
async def update_resume(
    resume_id: str,
    resume: ResumeUpdate,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Update a specific resume by ID.
    """
    updated_resume = await resume_service.update_resume(resume_id, resume, current_user.id)
    if not updated_resume:
        raise HTTPException(status_code=404, detail="Resume not found")
    return updated_resume


@router.delete("/{resume_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_resume(
    resume_id: str,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Delete a specific resume by ID.
    """
    success = await resume_service.delete_resume(resume_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="Resume not found")
    return None


@router.post("/{resume_id}/generate", response_model=ResumeResponse)
async def generate_resume_content(
    resume_id: str,
    job_description: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Generate content for a resume using AI.
    """
    resume = await resume_service.generate_resume_content(resume_id, current_user.id, job_description)
    if not resume:
        raise HTTPException(status_code=404, detail="Resume not found")
    return resume


@router.post("/{resume_id}/analyze", response_model=dict)
async def analyze_resume(
    resume_id: str,
    job_description: str,
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Analyze a resume against a job description to get ATS score and suggestions.
    """
    analysis = await resume_service.analyze_resume(resume_id, current_user.id, job_description)
    if not analysis:
        raise HTTPException(status_code=404, detail="Resume not found")
    return analysis


@router.post("/{resume_id}/export", response_model=dict)
async def export_resume(
    resume_id: str,
    format: str = "pdf",
    current_user: User = Depends(get_current_user),
    resume_service: ResumeService = Depends(),
):
    """
    Export a resume to PDF or other formats.
    """
    export_url = await resume_service.export_resume(resume_id, current_user.id, format)
    if not export_url:
        raise HTTPException(status_code=404, detail="Resume not found")
    return {"url": export_url}


@router.get("/pdf-preview/{file_path:path}")
@limiter.limit("100/minute")
async def get_pdf_preview_url(
    request: Request,
    file_path: str,
    current_user: User = Depends(get_current_user),
):
    """
    Generate a signed URL for PDF preview with security validation.

    Args:
        file_path: The path to the file in Supabase storage
        current_user: The authenticated user

    Returns:
        dict: Contains the signed URL for the PDF

    Raises:
        HTTPException: If file not found or access denied
    """
    logger = logging.getLogger(__name__)
    logger.info(f"PDF preview request for file_path: {file_path}, user_id: {current_user.id}")

    try:
        # Validate file_path to prevent path traversal attacks
        if ".." in file_path or file_path.startswith("/") or not file_path:
            raise HTTPException(
                status_code=400,
                detail="Invalid file path"
            )

        # Validate file path format (should be user_id/file_uuid.pdf)
        path_pattern = r'^[a-f0-9\-]+/[a-f0-9\-]+\.(pdf|docx)$'
        if not re.match(path_pattern, file_path):
            raise HTTPException(
                status_code=400,
                detail="Invalid file path format"
            )

        # Extract user_id from file path and verify ownership
        user_id_from_path = file_path.split('/')[0]
        if user_id_from_path != str(current_user.id):
            raise HTTPException(
                status_code=403,
                detail="Access denied: You can only access your own files"
            )

        # Generate signed URL with short expiry (60 seconds)
        logger.info(f"Generating signed URL for file_path: {file_path}")
        signed_url_response = supabase.storage.from_("resumes").create_signed_url(
            file_path,
            expires_in=60
        )

        logger.info(f"Supabase response: {signed_url_response}")

        if signed_url_response.get("error") or not signed_url_response.get("signedURL"):
            logger.error(f"Supabase error: {signed_url_response.get('error')}")
            raise HTTPException(
                status_code=404,
                detail="File not found or access denied"
            )

        signed_url = signed_url_response["signedURL"]

        if not signed_url:
            raise HTTPException(
                status_code=404,
                detail="File not found"
            )

        return {
            "url": signed_url,
            "expires_in": 60,
            "file_path": file_path
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in PDF preview: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error generating signed URL: {str(e)}"
        )
