from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID

class Education(BaseModel):
    institution: str
    degree: str
    field_of_study: Optional[str] = None
    start_date: str
    end_date: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None

class Experience(BaseModel):
    company: str
    position: str
    start_date: str
    end_date: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    achievements: Optional[List[str]] = None

class Skill(BaseModel):
    name: str
    level: Optional[str] = None
    category: Optional[str] = None

class Project(BaseModel):
    name: str
    description: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    url: Optional[str] = None
    technologies: Optional[List[str]] = None

class Language(BaseModel):
    name: str
    proficiency: str

class Certification(BaseModel):
    name: str
    issuer: str
    date: Optional[str] = None
    url: Optional[str] = None
    expiry_date: Optional[str] = None

class ResumeBase(BaseModel):
    title: str
    template_id: Optional[str] = None
    personal_info: Dict[str, Any]
    objective: Optional[str] = None
    education: Optional[List[Education]] = None
    experience: Optional[List[Experience]] = None
    skills: Optional[List[Skill]] = None
    projects: Optional[List[Project]] = None
    languages: Optional[List[Language]] = None
    certifications: Optional[List[Certification]] = None
    references: Optional[List[Dict[str, Any]]] = None
    custom_sections: Optional[Dict[str, Any]] = None

class ResumeCreate(ResumeBase):
    pass

class ResumeUpdate(ResumeBase):
    title: Optional[str] = None
    template_id: Optional[str] = None
    personal_info: Optional[Dict[str, Any]] = None

class ResumeResponse(ResumeBase):
    id: UUID
    user_id: UUID
    status: str = Field(..., description="Status of the resume: draft, completed, etc.")
    ats_score: Optional[float] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
