import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/ui/Button';
import { motion } from 'framer-motion';
import AuthModal from '../components/AuthModal';
import { uploadResume } from '../utils/resumeUtils';

// File size limit: 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024;
// Allowed file types
const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/msword': ['.doc']
};

// Check filename for resume-related keywords
const checkFileName = (fileName: string): number => {
  const lowerFileName = fileName.toLowerCase();
  const resumeKeywords = ['resume', 'cv', 'curriculum', 'vitae', 'career'];

  // Check if any resume keywords are in the filename
  for (const keyword of resumeKeywords) {
    if (lowerFileName.includes(keyword)) {
      return 20; // Award 20 points for resume-like filename
    }
  }

  // Check for negative indicators (invoice, receipt, etc.)
  const negativeKeywords = ['invoice', 'receipt', 'bill', 'statement', 'order', 'payment', 'quote'];
  for (const keyword of negativeKeywords) {
    if (lowerFileName.includes(keyword)) {
      return -30; // Subtract 30 points for invoice-like filename
    }
  }

  return 0; // Neutral if no keywords found
};

// Function to check if text contains resume patterns
const isLikelyResume = (text: string, fileName: string) => {
  // Convert to lowercase for case-insensitive matching
  const lowerText = text.toLowerCase();

  // Initialize confidence with filename check
  let confidence = checkFileName(fileName);

  // Check for section headers (minimum 2 for confidence)
  const sectionHeaders = [
    'experience', 'education', 'skills', 'work history',
    'employment', 'professional experience', 'summary',
    'objective', 'qualifications', 'certifications', 'achievements',
    'projects', 'publications', 'languages', 'interests', 'references'
  ];

  const sectionHeadersFound = sectionHeaders.filter(
    header => lowerText.includes(header)
  );

  // Check for contact pattern (email is a strong indicator)
  const hasEmail = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/.test(text);

  // Check for phone number pattern
  const hasPhone = /(\+\d{1,3}[\s-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/.test(text);

  // Check for date patterns
  const hasDatePattern = /\b(19|20)\d{2}\s*(-|–|to)\s*(19|20)\d{2}|present\b/i.test(text) ||
                         /\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s*\d{4}\b/i.test(text);

  // Check for negative indicators (invoice-specific terms)
  const invoiceTerms = [
    'invoice', 'bill to', 'payment due', 'invoice number', 'total due',
    'subtotal', 'tax amount', 'invoice date', 'due date', 'purchase order',
    'quantity', 'unit price', 'amount due', 'payment terms'
  ];
  const invoiceTermsFound = invoiceTerms.filter(term => lowerText.includes(term));

  // Check for resume-specific phrases
  const resumePhrases = [
    'professional profile', 'career objective', 'work experience',
    'job history', 'references', 'technical skills', 'soft skills',
    'professional summary', 'career highlights', 'core competencies'
  ];
  const resumePhrasesFound = resumePhrases.filter(phrase => lowerText.includes(phrase));

  // Calculate confidence score
  confidence += sectionHeadersFound.length * 15; // 15 points per section found
  confidence += hasEmail ? 25 : 0;
  confidence += hasPhone ? 15 : 0;
  confidence += hasDatePattern ? 15 : 0;
  confidence -= invoiceTermsFound.length * 20; // Subtract 20 points per invoice term
  confidence += resumePhrasesFound.length * 10; // 10 points per resume phrase

  // If we found multiple invoice terms, it's very likely an invoice
  if (invoiceTermsFound.length >= 3) {
    confidence -= 50; // Heavy penalty for multiple invoice terms
  }

  return {
    isResume: confidence >= 40, // Lower threshold but with better checks
    confidence: Math.max(0, Math.min(confidence, 100)), // Clamp between 0-100
    sectionsFound: [...sectionHeadersFound, ...resumePhrasesFound],
    invoiceTermsFound: invoiceTermsFound
  };
};

// Extract text from plain text files
const extractTextFromTXT = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const text = event.target?.result as string;
        resolve(text);
      } catch (error) {
        reject(new Error('Failed to extract text from text file'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
};

// Basic text extraction from binary files (PDF, DOCX)
const extractTextFromBinary = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        // For binary files, we'll just do a basic check for common resume keywords
        // in the raw binary data, which isn't perfect but can catch some patterns
        const arrayBuffer = event.target?.result as ArrayBuffer;
        const textDecoder = new TextDecoder('utf-8');
        const firstChunk = new Uint8Array(arrayBuffer.slice(0, 10240)); // First 10KB
        const text = textDecoder.decode(firstChunk);
        resolve(text);
      } catch (error) {
        // If we can't extract text, just return the filename as a fallback
        // This will at least let us check if the filename contains resume-related terms
        resolve(file.name);
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsArrayBuffer(file);
  });
};

// Extract text based on file type
const extractText = async (file: File): Promise<string> => {
  // For very large files, only read a portion to avoid browser performance issues
  const maxSize = 1024 * 1024; // 1MB

  // Create a new File object with the sliced content if needed
  let fileToProcess = file;
  if (file.size > maxSize) {
    const slice = file.slice(0, maxSize);
    // Create a new File object from the slice, preserving the name and type
    fileToProcess = new File([slice], file.name, { type: file.type });
  }

  if (file.type === 'text/plain') {
    return extractTextFromTXT(fileToProcess);
  } else {
    // For PDF and DOCX files, use the binary extraction method
    return extractTextFromBinary(fileToProcess);
  }
};

const ResumeOptimizerPage: React.FC = () => {
  const { isDark } = useTheme();
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  const [file, setFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isValidatingResume, setIsValidatingResume] = useState(false);
  const [resumeValidation, setResumeValidation] = useState<{
    isResume: boolean;
    confidence: number;
    sectionsFound: string[];
    invoiceTermsFound?: string[];
  } | null>(null);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [resumeId, setResumeId] = useState<string | null>(null);

  // State for override validation
  const [overrideValidation, setOverrideValidation] = useState(false);

  // Check if we need to process a file after login
  useEffect(() => {
    // If we have a file and the user just logged in, process the file
    if (file && isAuthenticated && resumeId === null && !isUploading && !isAnalyzing) {
      handleSubmit(new Event('submit') as any);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, file, resumeId, isUploading, isAnalyzing]);

  // Handle file drop
  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Reset errors and validation
    setFileError(null);
    setResumeValidation(null);
    setOverrideValidation(false);

    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const error = rejectedFiles[0].errors[0];
      if (error.code === 'file-too-large') {
        setFileError(`File is too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`);
      } else if (error.code === 'file-invalid-type') {
        setFileError('Invalid file type. Please upload a PDF or DOCX file.');
      } else {
        setFileError('Error uploading file. Please try again.');
      }
      return;
    }

    // Handle accepted files
    if (acceptedFiles.length > 0) {
      const selectedFile = acceptedFiles[0];

      // Additional security check for file type
      const validTypes = Object.keys(ACCEPTED_FILE_TYPES);
      const validExtensions = ['pdf', 'docx', 'doc', 'txt'];
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();

      if (!validTypes.includes(selectedFile.type) &&
          (!fileExtension || !validExtensions.includes(fileExtension))) {
        setFileError('Invalid file type. Please upload a PDF, DOCX, DOC, or TXT file.');
        return;
      }

      // Additional security check for file size
      if (selectedFile.size > MAX_FILE_SIZE) {
        setFileError(`File is too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`);
        return;
      }

      // Set file and start resume validation
      setFile(selectedFile);
      setIsValidatingResume(true);

      try {
        // Extract text from file
        const text = await extractText(selectedFile);

        // Validate if it's a resume
        const validation = isLikelyResume(text, selectedFile.name);
        setResumeValidation(validation);

        // If not a resume, show error
        if (!validation.isResume) {
          let errorMessage = `This doesn't appear to be a resume (${validation.confidence}% confidence).\n`;

          if (validation.sectionsFound.length > 0) {
            errorMessage += `We found these resume sections: ${validation.sectionsFound.join(', ')}.\n`;
          } else {
            errorMessage += 'No resume sections were detected.\n';
          }

          if (validation.invoiceTermsFound && validation.invoiceTermsFound.length > 0) {
            errorMessage += `We found invoice-related terms: ${validation.invoiceTermsFound.join(', ')}.\n`;
          }

          errorMessage += 'Please check your file and try again, or click "This is definitely a resume" if you believe this is incorrect.';

          setFileError(errorMessage);
        }
      } catch (error) {
        setFileError('Error analyzing file. Please try again.');
      } finally {
        setIsValidatingResume(false);
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    multiple: false
  });

  // Handle auth modal
  const openAuthModal = () => setAuthModalOpen(true);
  const closeAuthModal = () => {
    setAuthModalOpen(false);
    // If the user is now authenticated after closing the modal, we'll process the file
    // This is handled by the useEffect
  };

  // Handle override validation
  const handleOverrideValidation = () => {
    setOverrideValidation(true);
    setFileError(null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setFileError('Please upload a resume file.');
      return;
    }

    // Check if the file is a resume or if validation has been overridden
    if (!overrideValidation && resumeValidation && !resumeValidation.isResume) {
      setFileError('This file doesn\'t appear to be a resume. Please upload a valid resume or click "This is definitely a resume" to proceed anyway.');
      return;
    }

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Open auth modal if not authenticated
      openAuthModal();
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // Set up upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 95) {
          clearInterval(progressInterval);
          return 95;
        }
        return prev + 5;
      });
    }, 100);

    try {
      // Upload the file to Supabase and create a resume record
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Upload the resume file and create a record in the database
      const newResumeId = await uploadResume(file, user.id);
      setResumeId(newResumeId);

      // Complete upload
      clearInterval(progressInterval);
      setUploadProgress(100);
      setIsUploading(false);

      // Start analysis
      setIsAnalyzing(true);

      // In a real implementation, we would trigger an analysis job here
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Analysis complete - redirect to results page
      setIsAnalyzing(false);

      // Redirect to results page with the file ID
      navigate(`/resume-results?id=${newResumeId}`);

    } catch (error: any) {
      // Log error in production environment using proper error logging
      setFileError(`Error uploading file: ${error.message || 'Please try again.'}`);
      setIsUploading(false);
      clearInterval(progressInterval);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-3xl mx-auto">
        <h1 className={`text-3xl md:text-4xl font-bold mb-6 text-center ${isDark ? 'text-white' : 'text-gray-800'}`}>
          Resume Optimizer
        </h1>
        <p className={`text-lg mb-8 text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
          Upload your resume to get an ATS compatibility score and AI-powered optimization suggestions.
        </p>

        <div className={`rounded-lg p-8 mb-8 ${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow-lg'}`}>
          <h2 className={`text-xl font-semibold mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
            Upload Your Resume
          </h2>

          <form onSubmit={handleSubmit}>
            {/* File Drop Zone */}
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 mb-4 text-center cursor-pointer transition-colors
                ${isDragActive
                  ? isDark
                    ? 'border-neon-cyan bg-dark-300'
                    : 'border-blue-500 bg-blue-50'
                  : isDark
                    ? 'border-gray-600 hover:border-neon-cyan'
                    : 'border-gray-300 hover:border-blue-500'
                }
                ${fileError ? isDark ? 'border-red-500' : 'border-red-500' : ''}
                ${resumeValidation?.isResume ? isDark ? 'border-green-500' : 'border-green-500' : ''}
              `}
            >
              <input {...getInputProps()} />

              <div className="flex flex-col items-center justify-center">
                {isValidatingResume ? (
                  // Validating resume spinner
                  <div className="flex flex-col items-center">
                    <svg className={`animate-spin w-12 h-12 mb-4 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      Analyzing document...
                    </p>
                  </div>
                ) : file ? (
                  // File selected
                  <div className="flex flex-col items-center">
                    {resumeValidation?.isResume ? (
                      // Valid resume
                      <svg
                        className={`w-12 h-12 mb-4 ${isDark ? 'text-green-400' : 'text-green-500'}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    ) : (
                      // File selected but not validated or invalid
                      <svg
                        className={`w-12 h-12 mb-4 ${isDark ? 'text-gray-400' : 'text-gray-400'}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    )}
                    <p className={`text-lg ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      <span className="font-medium">Selected file:</span> {file.name}
                    </p>
                    {resumeValidation?.isResume && (
                      <p className={`text-sm mt-2 ${isDark ? 'text-green-400' : 'text-green-600'}`}>
                        ✓ Resume detected! Ready to optimize.
                      </p>
                    )}
                  </div>
                ) : (
                  // No file selected
                  <>
                    <svg
                      className={`w-12 h-12 mb-4 ${isDark ? 'text-gray-400' : 'text-gray-400'}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p className={`text-lg font-medium mb-1 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                      {isDragActive ? 'Drop your resume here' : 'Drag & drop your resume here'}
                    </p>
                    <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      or click to browse files
                    </p>
                    <p className={`text-xs mt-2 ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>
                      Supported formats: PDF, DOCX, DOC (Max 5MB)
                    </p>
                  </>
                )}
              </div>
            </div>

            {/* Error Message */}
            {fileError && (
              <div className={`p-3 mb-4 rounded-md ${isDark ? 'bg-red-900/30 text-red-200' : 'bg-red-50 text-red-500'}`}>
                <p className="text-sm whitespace-pre-line">{fileError}</p>

                {/* Override button - only show for resume validation errors */}
                {resumeValidation && !resumeValidation.isResume && !overrideValidation && (
                  <button
                    type="button"
                    onClick={handleOverrideValidation}
                    className={`mt-3 px-3 py-1 text-xs font-medium rounded-md ${
                      isDark
                        ? 'bg-dark-400 text-white hover:bg-dark-500'
                        : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                    }`}
                  >
                    This is definitely a resume
                  </button>
                )}
              </div>
            )}

            {/* Upload Progress */}
            {isUploading && (
              <div className="mb-4">
                <div className="flex justify-between mb-1">
                  <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Uploading...</span>
                  <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>{uploadProgress}%</span>
                </div>
                <div className={`w-full h-2 rounded-full ${isDark ? 'bg-dark-300' : 'bg-gray-200'}`}>
                  <div
                    className={`h-2 rounded-full ${isDark ? 'bg-neon-cyan' : 'bg-blue-500'}`}
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Analyzing State */}
            {isAnalyzing && (
              <div className={`p-4 mb-4 rounded-md ${isDark ? 'bg-dark-300' : 'bg-blue-50'}`}>
                <div className="flex items-center">
                  <svg className={`animate-spin -ml-1 mr-3 h-5 w-5 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className={`${isDark ? 'text-gray-200' : 'text-blue-700'}`}>Analyzing your resume...</span>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-center">
              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  disabled={!file || isUploading || isAnalyzing}
                >
                  {isUploading ? 'Uploading...' : isAnalyzing ? 'Analyzing...' : 'Analyze Resume'}
                </Button>
              </motion.div>
            </div>
          </form>
        </div>

        {/* Information Section */}
        <div className={`rounded-lg p-6 ${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow-lg'}`}>
          <h2 className={`text-xl font-semibold mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
            Why Optimize Your Resume?
          </h2>
          <ul className={`list-disc pl-5 space-y-2 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            <li>75% of resumes are rejected by ATS before a human ever sees them</li>
            <li>Proper formatting and keywords significantly increase your chances</li>
            <li>Our AI analyzes your resume against industry standards</li>
            <li>Get personalized suggestions to improve your resume's effectiveness</li>
          </ul>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={closeAuthModal}
        initialMode="signup"
      />
    </div>
  );
};

export default ResumeOptimizerPage;
