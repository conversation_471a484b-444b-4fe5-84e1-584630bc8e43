// PDF Proxy Worker for FajiraPro

// Define constants
const SUPABASE_URL = 'https://xmmbjopzpyotkyyfqzyq.supabase.co';

// Modern Cloudflare Workers use the Modules format
export default {
  async fetch(request, env, ctx) {
    return await handleRequest(request, env);
  }
};

async function handleRequest(request, env) {
  // Use the service role key for full access to Supabase
  const SUPABASE_API_KEY = env.SUPABASE_SERVICE_ROLE_KEY || env.SUPABASE_API_KEY;

  // Parse the URL and extract the path
  const url = new URL(request.url);
  const path = url.pathname;

  // Test endpoint to check Supabase connection
  if (path === '/pdf/test') {
    return handleTestRequest(SUPABASE_API_KEY);
  }

  // Handle OPTIONS requests for CORS
  if (request.method === 'OPTIONS') {
    return handleCORS();
  }

  // Handle PDF requests
  if (path.startsWith('/pdf/') && path !== '/pdf/test') {
    // Extract just the resume ID from the path
    // The path format is /pdf/resume_id
    const pathParts = path.split('/').filter(part => part.length > 0);

    // The resume ID should be the second part after 'pdf'
    // e.g., /pdf/67abde20-2ea1-4249-9f23-71e79150a1fd
    if (pathParts.length >= 2 && pathParts[0] === 'pdf') {
      const resumeId = pathParts[1];

      // Log the request for debugging
      console.log(`PDF request for resume ID: ${resumeId}`);

      return handlePdfRequest(resumeId, SUPABASE_API_KEY);
    }

    // If we can't parse the path properly, return a 400 error
    return new Response(JSON.stringify({
      success: false,
      message: 'Invalid PDF request path',
      path: path
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }

  // Default response for other paths
  return new Response('Not Found', { status: 404 });
}

async function handleTestRequest(SUPABASE_API_KEY) {
  try {
    // Try to connect to Supabase and get resume data
    const resumeResponse = await fetch(`${SUPABASE_URL}/rest/v1/resumes?limit=1`, {
      headers: {
        'apikey': SUPABASE_API_KEY,
        'Authorization': `Bearer ${SUPABASE_API_KEY}`
      }
    });

    let resumeData = null;
    let resumeError = null;

    if (resumeResponse.ok) {
      resumeData = await resumeResponse.json();
    } else {
      resumeError = `${resumeResponse.status}: ${await resumeResponse.text()}`;
    }

    // Check storage buckets
    const bucketsResponse = await fetch(`${SUPABASE_URL}/storage/v1/bucket`, {
      headers: {
        'apikey': SUPABASE_API_KEY,
        'Authorization': `Bearer ${SUPABASE_API_KEY}`
      }
    });

    let bucketsData = null;
    let bucketsError = null;

    if (bucketsResponse.ok) {
      bucketsData = await bucketsResponse.json();
    } else {
      bucketsError = `${bucketsResponse.status}: ${await bucketsResponse.text()}`;
    }

    // Return the test results
    return new Response(JSON.stringify({
      success: true,
      message: 'Supabase connection test',
      supabase_url: SUPABASE_URL,
      api_key_set: !!SUPABASE_API_KEY,
      api_key_length: SUPABASE_API_KEY ? SUPABASE_API_KEY.length : 0,
      resume_table: {
        status: resumeResponse.status,
        ok: resumeResponse.ok,
        data: resumeData,
        error: resumeError
      },
      storage_buckets: {
        status: bucketsResponse.status,
        ok: bucketsResponse.ok,
        data: bucketsData,
        error: bucketsError
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      message: 'Error testing Supabase connection',
      error: error.message,
      stack: error.stack
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

async function handlePdfRequest(resumeId, SUPABASE_API_KEY) {
  try {
    console.log(`Processing PDF request for resume ID: ${resumeId}`);

    if (!SUPABASE_API_KEY) {
      console.error('SUPABASE_SERVICE_ROLE_KEY is not set');
      return new Response(JSON.stringify({
        success: false,
        message: 'Server configuration error: API key not set',
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // Step 1: Get the resume data from Supabase
    console.log(`Fetching resume data for ID: ${resumeId}`);
    const resumeResponse = await fetch(`${SUPABASE_URL}/rest/v1/resumes?id=eq.${resumeId}&select=*`, {
      headers: {
        'apikey': SUPABASE_API_KEY,
        'Authorization': `Bearer ${SUPABASE_API_KEY}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      }
    });

    if (!resumeResponse.ok) {
      const errorText = await resumeResponse.text();
      console.error(`Error fetching resume data: ${resumeResponse.status} - ${errorText}`);
      return new Response(JSON.stringify({
        success: false,
        message: 'Error fetching resume data',
        status: resumeResponse.status,
        error: errorText,
        resumeId: resumeId
      }), {
        status: resumeResponse.status,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    const resumeData = await resumeResponse.json();

    if (!resumeData || resumeData.length === 0) {
      console.error(`Resume not found: ${resumeId}`);
      return new Response(JSON.stringify({
        success: false,
        message: 'Resume not found',
        resumeId: resumeId
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // Step 2: Extract the file path
    const resume = resumeData[0];
    console.log(`Resume data: ${JSON.stringify(resume)}`);

    const filePath = resume.file_path;

    if (!filePath) {
      console.error(`File path not found in resume data: ${JSON.stringify(resume)}`);
      return new Response(JSON.stringify({
        success: false,
        message: 'File path not found in resume data',
        resumeData: resume,
        availableFields: Object.keys(resume)
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    console.log(`File path from resume data: ${filePath}`);

    // Step 3: Get a signed URL for the file
    console.log(`Generating signed URL for file: ${filePath}`);
    const signResponse = await fetch(`${SUPABASE_URL}/storage/v1/object/sign/resumes/${filePath}`, {
      method: 'POST',
      headers: {
        'apikey': SUPABASE_API_KEY,
        'Authorization': `Bearer ${SUPABASE_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        expiresIn: 3600 // 1 hour expiry
      })
    });

    if (!signResponse.ok) {
      const errorText = await signResponse.text();
      console.error(`Error generating signed URL: ${signResponse.status} - ${errorText}`);
      return new Response(JSON.stringify({
        success: false,
        message: 'Error generating signed URL',
        status: signResponse.status,
        error: errorText,
        filePath: filePath
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    const signedUrlData = await signResponse.json();
    console.log(`Signed URL response: ${JSON.stringify(signedUrlData)}`);

    if (!signedUrlData.signedURL) {
      return new Response(JSON.stringify({
        success: false,
        message: 'No signed URL returned from Supabase',
        signedUrlData
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    const signedUrl = signedUrlData.signedURL;

    // Step 4: Fetch the actual PDF using the signed URL
    console.log(`Fetching PDF from signed URL: ${signedUrl}`);

    // Make sure the URL is absolute
    const absoluteUrl = signedUrl.startsWith('http') ? signedUrl : `${SUPABASE_URL}${signedUrl}`;
    console.log(`Using absolute URL: ${absoluteUrl}`);

    try {
      const pdfResponse = await fetch(absoluteUrl);

      if (!pdfResponse.ok) {
        const errorText = await pdfResponse.text();
        console.error(`Error fetching PDF: ${pdfResponse.status} - ${errorText}`);
        return new Response(JSON.stringify({
          success: false,
          message: 'Error fetching PDF',
          status: pdfResponse.status,
          error: errorText,
          url: absoluteUrl
        }), {
          status: pdfResponse.status,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      // Step 5: Return the PDF with appropriate headers
      console.log(`Successfully fetched PDF, returning response`);
      return new Response(pdfResponse.body, {
        headers: {
          // Content type and disposition
          'Content-Type': 'application/pdf',
          'Content-Disposition': `inline; filename="resume-${resumeId}.pdf"`,

          // Caching headers
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour

          // CORS headers
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',

          // Content Security Policy
          'Content-Security-Policy': "default-src 'self'; frame-ancestors 'self' https://ajirapro.com https://fajirapro.com; object-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline';",

          // Additional security headers
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'SAMEORIGIN'
        }
      });
    } catch (fetchError) {
      console.error(`Error fetching PDF: ${fetchError.message}`);
      return new Response(JSON.stringify({
        success: false,
        message: 'Error fetching PDF',
        error: fetchError.message,
        url: absoluteUrl
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  } catch (error) {
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
    return new Response(JSON.stringify({
      success: false,
      message: 'Unhandled error in PDF proxy',
      error: error.message,
      stack: error.stack
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

function handleCORS() {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  });
}
