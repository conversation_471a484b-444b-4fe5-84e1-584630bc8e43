import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTheme } from "../contexts/ThemeContext";
import { useAuth } from "../contexts/AuthContext";
import Button from "../components/ui/Button";
import { motion } from "framer-motion";
import { getResume, updateResumeAtsScore } from "../utils/resumeUtils";
import EnhancedPDFViewer from "../components/EnhancedPDFViewer";

// Resume data interface
interface ResumeData {
  id: string;
  fileName: string;
  filePath?: string | null;
  ats_score: number;
  sections: {
    name: string;
    score: number;
    suggestions: string[];
  }[];
  overallSuggestions: string[];
}

// Default structure for new resumes
const getDefaultResumeData = (
  id: string,
  fileName: string,
  atsScore: number
): ResumeData => ({
  id,
  fileName,
  filePath: null,
  ats_score: atsScore,
  sections: [
    { name: "Contact Information", score: 90, suggestions: ["Add LinkedIn profile"] },
    { name: "Professional Summary", score: 75, suggestions: ["Make it concise", "Add keywords"] },
    { name: "Work Experience", score: 65, suggestions: ["Use action verbs", "Quantify achievements"] },
    { name: "Education", score: 85, suggestions: ["Add relevant coursework"] },
    { name: "Skills", score: 50, suggestions: ["Add technical skills", "Organize by category"] },
    { name: "Formatting", score: 60, suggestions: ["Improve spacing", "Use consistent fonts"] },
  ],
  overallSuggestions: [
    "Add industry-specific keywords",
    "Quantify achievements",
    "Ensure consistent formatting",
    "Use bullet points for readability",
    "Include a technical skills section",
  ],
});

const ResumeResultsPage: React.FC = () => {
  const { isDark } = useTheme();
  const { isAuthenticated } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [resumeData, setResumeData] = useState<ResumeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const searchParams = new URLSearchParams(location.search);
  let resumeId = searchParams.get("id");

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/resume-optimizer", { replace: true });
      return;
    }

    const fetchResumeData = async () => {
      try {
        if (!resumeId) throw new Error("No resume ID provided.");

        // Fetch the resume data
        const resume = await getResume(resumeId);

        // Generate a random ATS score if none exists
        if (resume.ats_score === null) {
          const randomScore = Math.floor(Math.random() * 41) + 50;
          await updateResumeAtsScore(resumeId, randomScore);
          resume.ats_score = randomScore;
        }

        const formattedData = {
          ...getDefaultResumeData(resume.id, resume.title, resume.ats_score || 68),
          filePath: resume.file_path,
        };

        setResumeData(formattedData);
      } catch (err: any) {
        setError(err.message || "Failed to load resume data.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchResumeData();
  }, [resumeId, isAuthenticated, navigate]);

  const getScoreColor = (score: number) =>
    score >= 80 ? (isDark ? "text-green-400" : "text-green-600") :
    score >= 60 ? (isDark ? "text-yellow-400" : "text-yellow-600") :
                  (isDark ? "text-red-400" : "text-red-600");

  const handleApplyRecommendations = () =>
    alert("Recommendations applied. Resume optimized!");

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <svg className="animate-spin w-12 h-12 mx-auto mb-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <h2 className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-800"}`}>
            Loading Resume Analysis
          </h2>
          <p className={`${isDark ? "text-gray-300" : "text-gray-600"}`}>
            Please wait while we prepare your resume analysis...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center max-w-md">
          <svg className="w-16 h-16 mx-auto mb-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-800"}`}>
            Error Loading Resume
          </h2>
          <p className={`mb-4 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 rounded-md font-medium ${
              isDark
                ? "bg-blue-600 hover:bg-blue-700 text-white"
                : "bg-blue-500 hover:bg-blue-600 text-white"
            }`}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!resumeData) return null;

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen flex flex-col">
      <h1 className={`text-3xl font-bold mb-6 flex-shrink-0 ${isDark ? "text-white" : "text-gray-800"}`}>
        Resume Analysis Results
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 flex-1 min-h-0" style={{ minHeight: 'calc(100vh - 200px)' }}>
        {/* Left column: PDF Preview */}
        <div className={`rounded-lg overflow-hidden shadow-lg flex flex-col ${isDark ? "bg-dark-200" : "bg-white"}`}>
          <h2 className={`text-xl font-semibold p-4 border-b flex-shrink-0 ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
            Resume Preview
          </h2>
          <div className="flex-1 min-h-0">
            {resumeData.filePath && (
              <EnhancedPDFViewer
                filePath={resumeData.filePath}
                fileName={resumeData.fileName}
                className="w-full h-full"
                onError={(error) => setError(error)}
              />
            )}
          </div>
        </div>

        {/* Right column: ATS Score and Suggestions */}
        <div className="flex flex-col">
          <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-shrink-0 ${isDark ? "bg-dark-200" : "bg-white"}`}>
            <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
              ATS Compatibility Score
            </h2>
            <div className="p-6">
              <div className="flex items-center justify-between mb-2">
                <span className={`text-lg font-medium ${isDark ? "text-white" : "text-gray-800"}`}>Overall Score</span>
                <span className={`text-2xl font-bold ${getScoreColor(resumeData.ats_score)}`}>
                  {resumeData.ats_score}%
                </span>
              </div>

              {/* Progress bar */}
              <div className={`w-full h-4 rounded-full ${isDark ? "bg-dark-300" : "bg-gray-200"}`}>
                <div
                  className={`h-4 rounded-full ${
                    resumeData.ats_score >= 80 ? "bg-green-500" :
                    resumeData.ats_score >= 60 ? "bg-yellow-500" :
                    "bg-red-500"
                  }`}
                  style={{ width: `${resumeData.ats_score}%` }}
                ></div>
              </div>

              <p className={`mt-4 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                {resumeData.ats_score >= 80
                  ? "Great job! Your resume is well-optimized for ATS systems."
                  : resumeData.ats_score >= 60
                  ? "Your resume is moderately optimized. Consider the suggestions below to improve."
                  : "Your resume needs significant improvements to pass ATS systems."
                }
              </p>
            </div>
          </div>

          {/* Section Scores */}
          <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-1 flex flex-col ${isDark ? "bg-dark-200" : "bg-white"}`}>
            <h2 className={`text-xl font-semibold p-4 border-b flex-shrink-0 ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
              Section Scores
            </h2>
            <div className="p-4 flex-1 overflow-y-auto">
              {resumeData.sections.map((section, index) => (
                <div
                  key={index}
                  className={`p-3 mb-2 rounded cursor-pointer ${
                    activeSection === section.name
                      ? isDark ? "bg-dark-100" : "bg-blue-50"
                      : isDark ? "hover:bg-dark-100" : "hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveSection(activeSection === section.name ? null : section.name)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className={`font-medium ${isDark ? "text-white" : "text-gray-800"}`}>{section.name}</span>
                    <span className={`font-bold ${getScoreColor(section.score)}`}>{section.score}%</span>
                  </div>

                  {/* Progress bar */}
                  <div className={`w-full h-2 rounded-full ${isDark ? "bg-dark-300" : "bg-gray-200"}`}>
                    <div
                      className={`h-2 rounded-full ${
                        section.score >= 80 ? "bg-green-500" :
                        section.score >= 60 ? "bg-yellow-500" :
                        "bg-red-500"
                      }`}
                      style={{ width: `${section.score}%` }}
                    ></div>
                  </div>

                  {/* Suggestions */}
                  {activeSection === section.name && section.suggestions.length > 0 && (
                    <div className={`mt-3 pl-3 border-l-2 ${isDark ? "border-gray-600 text-gray-300" : "border-gray-300 text-gray-600"}`}>
                      <p className="font-medium mb-1">Suggestions:</p>
                      <ul className="list-disc pl-5 space-y-1">
                        {section.suggestions.map((suggestion, i) => (
                          <li key={i}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Overall Suggestions */}
          <div className={`rounded-lg overflow-hidden shadow-lg flex-shrink-0 ${isDark ? "bg-dark-200" : "bg-white"}`}>
            <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
              Overall Recommendations
            </h2>
            <div className="p-6">
              <ul className={`list-disc pl-5 space-y-2 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                {resumeData.overallSuggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>

              <div className="mt-6 flex justify-center">
                <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handleApplyRecommendations}
                  >
                    Apply Recommendations
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResumeResultsPage;
