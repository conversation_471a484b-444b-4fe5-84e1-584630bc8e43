import os
import logging
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
try:
    load_dotenv()
    logger.info("Loaded environment variables from .env file")
except Exception as e:
    logger.warning(f"Failed to load .env file: {str(e)}")

# Log environment variables for debugging (without exposing sensitive values)
logger.info(f"SUPABASE_URL environment variable: {'Set' if os.getenv('SUPABASE_URL') else 'Not set'}")
logger.info(f"SUPABASE_KEY environment variable: {'Set' if os.getenv('SUPABASE_KEY') else 'Not set'}")
logger.info(f"REDIS_URL environment variable: {'Set' if os.getenv('REDIS_URL') else 'Not set'}")
logger.info(f"ENVIRONMENT: {os.getenv('ENVIRONMENT', 'Not set')}")
logger.info(f"DEBUG: {os.getenv('DEBUG', 'Not set')}")

class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "AjiraPro API"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    API_PREFIX: str = "/api"

    # Supabase settings
    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "")
    SUPABASE_KEY: str = os.getenv("SUPABASE_KEY", "")

    # OpenAI settings
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # Redis settings
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # Flutterwave settings
    FLUTTERWAVE_SECRET_KEY: str = os.getenv("FLUTTERWAVE_SECRET_KEY", "")
    FLUTTERWAVE_PUBLIC_KEY: str = os.getenv("FLUTTERWAVE_PUBLIC_KEY", "")
    FLUTTERWAVE_ENCRYPTION_KEY: str = os.getenv("FLUTTERWAVE_ENCRYPTION_KEY", "")

    # CORS settings
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:5173",
        "https://ajirapro.com",
        "https://www.ajirapro.com",
        "https://fajirapro.com",
        "https://www.fajirapro.com",
        "https://ajirapro.pages.dev",
        "https://www.ajirapro.pages.dev",
        "https://fajirapro.pages.dev",
        "https://www.fajirapro.pages.dev",
    ]

    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()
